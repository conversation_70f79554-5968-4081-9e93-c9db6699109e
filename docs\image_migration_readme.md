# 文档图片迁移工具

## 功能描述

此工具用于将Obsidian Vault中的图片文件迁移到项目文档目录中，并更新Markdown文件中的图片链接格式。

执行此脚本将完成以下操作：

1. 将Obsidian Vault中的图片复制到各个文档目录下的images文件夹
2. 更新Markdown文件中的图片链接格式，从Obsidian格式 `![[/image.png]]` 转换为标准Markdown格式 `![image.png](images/image.png)`
3. 处理文档中的PDF和其他附件链接，从Obsidian格式 `![[document.pdf]]` 转换为标准Markdown格式 `[document.pdf](document.pdf)`

## 使用方法

1. 确保Python环境已安装（Python 3.6或以上版本）
2. 将`migrate_images.py`放置在项目根目录
3. 根据需要修改脚本中的配置参数：
   - `source_dir`: Obsidian Vault的路径
   - `doc_dirs`: 需要处理的文档目录列表
4. 在命令行中执行脚本：

   ```
   python migrate_images.py
   ```

## 处理的文档目录

脚本默认处理以下六个文档目录：

- `docs/output/usage/lenAdjust`: 光路耦合软件文档
- `docs/output/usage/lenMEMD`: 手动录入MES软件文档
- `docs/output/usage/lenRework`: Rework软件文档
- `docs/support/lenMachine`: 设备支持文档
- `docs/support/mes`: MES支持文档
- `docs/development/modules/lenAdjust`: 开发文档中的lenAdjust模块文档

## 功能特点

1. 支持处理Obsidian格式的图片链接，包括以下两种格式：
   - `![[/image.png]]`
   - `![[/image.png|image name]]`

2. 支持处理PDF等附件链接，如：
   - `![[document.pdf]]` 会转换为 `[document.pdf](document.pdf)`
   - 支持的附件类型包括：pdf, doc, docx, xls, xlsx, ppt, pptx, txt

## 注意事项

1. 脚本会在每个文档目录下创建`images`子目录，用于存放迁移的图片
2. 脚本仅复制图片，不会删除Obsidian Vault中的原始图片
3. 如果图片在Obsidian Vault中不存在，脚本会输出警告但继续处理其他图片
4. 脚本会保留图片的原始文件名
5. 对于PDF等附件文件，脚本只会转换链接格式，不会复制或移动文件

## 输出格式

脚本执行过程中会输出详细的处理日志，包括：

- 创建的图片目录
- 处理的Markdown文件
- 复制的图片及其路径
- 转换的附件链接
- 遇到的任何错误或警告

执行成功后，会显示"图片迁移完成。"的消息。
