# 电机转速监控-增加PLC控制



## 方案：

原方案：

通过点击 startButton 开始和结束 检测

新需求：

增加一个串口，
1. plc 下发start/stop指令，达到和 startButton 一样的效果。
2. 每次测试后，需要给PLC发送一个状态，测试PASS/测试NG/出现异常

协议：modbus-rtu，可以使用 modbusRtu.cpp，或者临时解析
指令：自定义，简单就行
plc串口接收：使用QSerialPort::readyRead？触发的方式解析？

## UI

- 增加plc设备端口：ui->plcPortBox
- 增加端口开关 ui->serialPushButton

## workflow

open端口 -> plc端口数据监控 -> 解析启停指令 -> 开始结束任务 -> 发送测试结果状态

## 新增需求

每次测试后，需要给PLC发送一个状态：
- 测试PASS：发送状态值1到寄存器0x0002
- 测试NG：发送状态值2到寄存器0x0002
- 出现异常：发送状态值0到寄存器0x0002

在CMotorMonitorOperation::compAck中增加这条指令，指令地址使用0x0002