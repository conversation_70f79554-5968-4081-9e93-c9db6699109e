/*******************************
 * 非相连任务集处理，任务之间无联系，也无顺序上的关系
 *
 *******************************/

#ifndef PROCESSLISTB_H
#define PROCESSLISTB_H

#include <QElapsedTimer>
#include <QMetaType>
#include <QObject>
#include <QTime>
#include <QVector>
#include <stdint.h>

#include "processList.h"
#include "qLog.h"

#define TIMER_MS 5

template <typename ClassType, typename ParaType, typename T> class CProcessListB {
  public:
    CProcessListB() {
        // qRegisterMetaType<ECommStatus>("ECommStatus");
        qRegisterMetaType<EExecStatus>("EExecStatus");
        qRegisterMetaType<EResult>("EResult");
    };
    ~CProcessListB(){};

    struct StStepRecord {
        // ECommStatus       comm_result;

        T           last_step;  //执行状态机
        EExecStatus last_status;
        T           cur_step;         //执行状态机 步骤
        EExecStatus cur_exec_status;  //执行状态
        EExecStatus cur_ack_status;   //执行状态
        QString     unnormal_reason;  //
        bool        auto_normal_mode;

        bool          is_in_cycle;
        uint32_t      step_interval_time;
        QElapsedTimer step_timer;
        uint16_t      step_time;  //单步执行之间
        QElapsedTimer process_timer;
        uint32_t      exec_time;  //总运行时间
    };                            //运行时间计时

    // protected:
    typedef ParaType (ClassType::*fPtr)(void);  // const;

    //* 运行标志位
#if 0
    typedef struct{
        uint8_t    start   :1;
        uint8_t    stop    :1;
        uint8_t    ack     :1;
    } StTaskFlag;
#else
    typedef struct {
        bool exec;                                                //执行
        bool stop;                                                //
        bool task_flag;                                           //
    } StTaskFlag;                                                 //任务状态
#endif
    //    typedef union {
    //        uint16_t    errors;
    //        struct {
    //            uint16_t          start_send_err          :1;
    //            uint16_t          sensor_version          :1;
    //            uint16_t          query_table_err         :1;
    //            uint16_t          exec_error              :1;
    //         } all_error;
    //    } UProcessErrors;
    typedef struct {
        bool exec_status;  // 1-已执行， 0-未执行
        bool task_result;  // 1-任务正常，0-异常
    } StTaskExecStatus;
    typedef struct {
        StTaskFlag       flag;         //任务状态
        StTaskExecStatus task_status;  //执行状态

        fPtr start_func_;      //运行任务
        fPtr stop_func_;       //
        uint interval_time;    //运行间隔
        uint task_limit_time;  //运行时间限制
        uint time_cnt;         //运行时间计数
        uint ack_limit_time;   // ack等待时间限制
        uint reexec_times;     //重复执行次数
        uint reexec_cnt;
    } StTask;

    //  QVector<StTask>  mv_task_list;

    /**
     * @brief 只能添加本类中的函数
     * @param 开始函数、callback函数、ack标识符、运行间隔、超时时间
     * @param limit_time
     */
    StTask addCallbackFunction(fPtr        start_func_,
                               fPtr        stop_func_,
                               bool        task_flag,
                               const uint &interval_time,
                               const uint &limit_time,
                               const uint &ack_limit_time,
                               const uint &reexec_times) {
        StTask task;

        task.flag.exec      = false;
        task.flag.stop      = false;
        task.flag.task_flag = task_flag;

        task.task_status.exec_status = false;
        task.task_status.task_result = false;

        task.start_func_     = start_func_;
        task.stop_func_      = stop_func_;
        task.interval_time   = interval_time;
        task.time_cnt        = 0;
        task.task_limit_time = limit_time;
        task.ack_limit_time  = ack_limit_time;
        task.reexec_times    = reexec_times;
        task.reexec_cnt      = 0;
        return task;
        //    mv_task_list.append(task);
    }

    //    StTask addCallbackFunction(fPtr start_func_, fPtr stop_func_, \
//                               const uint &interval_time, const uint &limit_time) {
    //        StTask task;

    //        task.flag.exec = false;
    //        task.flag.stop = false;

    //        task.start_func_ = start_func_;
    //        task.stop_func_ = stop_func_;
    //        task.interval_time = interval_time;
    //        task.time_cnt = 0;
    //        task.task_limit_time = limit_time;

    //        return task;
    //    }

    void taskInit(QVector<StTask> *v_task_list_, StStepRecord *st_task_status_) {
        typename QVector<StTask>::iterator iter;

        for (iter = v_task_list_->begin(); iter != v_task_list_->end(); iter++) {
            iter->flag.exec = false;
            iter->flag.stop = false;

            iter->task_status.exec_status = false;
            iter->task_status.task_result = false;
            iter->time_cnt                = 0;
            iter->reexec_cnt              = 0;
        }

        st_task_status_->is_in_cycle        = false;
        st_task_status_->step_interval_time = 0;
        st_task_status_->step_time          = 0;
        st_task_status_->exec_time          = 0;
    }

    //**************************** 业务 tasks *****************************
    EExecStatus tasksRun(ClassType *pClassType, QVector<StTask> *v_task_list_, StStepRecord *st_task_status_) {
        typename QVector<StTask>::iterator iter;
        EExecStatus                        exec_status = eWAIT;
        for (iter = v_task_list_->begin(); iter != v_task_list_->end(); iter++) {

            if (iter->flag.exec) {  //
                if (iter->flag.task_flag) {

                    //* interval time
                    if (((st_task_status_->step_interval_time++) * TIMER_MS) < iter->interval_time) {
                        //                        qDebug() << "-i process/ step:" << st_task_status_->cur_step << "interval time cnt:" <<
                        //                        st_task_status_->step_interval_time;
                        break;  //
                    }

                    st_task_status_->step_interval_time = 0;
                    iter->flag.exec                     = false;
                    st_task_status_->cur_step           = T(iter - v_task_list_->begin());

                    //* 首任务，开始总任务计时
                    if (iter == v_task_list_->begin()) {
                        //                    st_task_status_->process_timer.start();
                        st_task_status_->process_timer.restart();
                    }
#ifdef PROCESS_STATUS_OUTPUT
                    //              QMetaEnum stepEnum = QMetaEnum::fromType<EProcessStep>();
                    //              QMetaEnum statusEnum = QMetaEnum::fromType<EExecStatus>();
                    //              QString step_str = stepEnum.valueToKey(st_task_status_->cur_step);
                    //              QString status_str = statusEnum.valueToKey(st_task_status_->cur_status);
                    //              qDebug() << "-i -clen/ process step: " << step_str << "status: " << status_str;
#endif

                    EExecStatus exec_flag = (pClassType->*(iter->start_func_))();
                    if (exec_flag > eERROR)
                        st_task_status_->is_in_cycle = true;

                    if (iter->stop_func_ == nullptr) {
                        st_task_status_->is_in_cycle = false;
                        if ((exec_flag) > 0) {  //非错误
                            if (iter != (v_task_list_->end() - 1))
                                (iter + 1)->flag.exec = true;
                            else {
                                if (exec_flag == eCOMP)
                                    exec_flag = ePROCESS_COMP;
                            }
                            //                    emit stepStatusSignal(st_task_status_->cur_step, st_task_status_->cur_status);//
                        } else if (exec_flag == eERROR) {  //非无法挽回错误，再次执行
                            if (iter->reexec_times != 0) {
                                if (iter->reexec_cnt++ < iter->reexec_times)
                                    iter->flag.exec = true;
                                else
                                    exec_flag = eFATAL;
                            } else
                                iter->flag.exec = true;
                        } else if (exec_flag == eFATAL) {  //人为处理
                            exec_status = eFATAL;
                            //                    emit stepStatusSignal(st_task_status_->cur_step, st_task_status_->cur_status);//
                        }
                    } else {
                        if ((exec_flag) == eCOMP) {  //非错误,开始计时
#if 1
                            iter->time_cnt = 0;
#else
                            st_task_status_->step_timer.start();  //
#endif
                            iter->flag.stop = true;  //执行ack任务
                        } else if (exec_flag == eOK) {
#if 1
                            iter->time_cnt = 0;
#else
                            st_task_status_->step_timer.start();  //
#endif
                            iter->flag.stop = false;  //等ack任务
                        }
                        //                  else if(exec_flag == eWAIT) ;
                        else if (exec_flag == eERROR) {
                            if (iter->reexec_times != 0) {
                                if (iter->reexec_cnt++ < iter->reexec_times)
                                    iter->flag.exec = true;
                                else
                                    exec_flag = eFATAL;
                            } else
                                iter->flag.exec = true;
                        } else if (exec_flag == eFATAL)
                            exec_status = eFATAL;  // emit stepStatusSignal(st_task_status_->cur_step, st_task_status_->cur_status);
                    }

                    exec_status                      = exec_flag;
                    st_task_status_->cur_exec_status = exec_flag;
                } else {
                    iter->flag.exec = false;
                    if (iter != (v_task_list_->end() - 1)) {
                        (iter + 1)->flag.exec = true;
                        exec_status           = eCOMP;
                    } else
                        exec_status = ePROCESS_COMP;
                }
            }
            if ((iter->stop_func_ != nullptr) && iter->flag.stop) {  //
                iter->flag.stop              = false;                //
                st_task_status_->is_in_cycle = false;

                st_task_status_->cur_step = T(iter - v_task_list_->begin());
                //* 任务结束计时
                if (iter == (v_task_list_->end() - 1)) {
                    st_task_status_->exec_time = st_task_status_->process_timer.elapsed();
                    //                    qDebug("-i process/ process_time:", QString::number(st_task_status_->exec_time, 10));
                }
                EExecStatus exec_flag = (pClassType->*(iter->stop_func_))();

                st_task_status_->step_time = st_task_status_->step_timer.elapsed();  //

                switch (exec_flag) {
                case eFATAL:  //异常，人为处理
                    //                emit stepStatusSignal(st_task_status_->cur_step, st_task_status_->cur_status); //
                    break;
                case eERROR:                 //
                    iter->flag.exec = true;  //可恢复错误，重新执行
                    break;
                case eWAIT:
                    break;
                case eOK:
                    iter->flag.exec = true;  //非结束，重复执行
                    //定时
                    break;
                case eCOMP:
                    if (iter != (v_task_list_->end() - 1))
                        (iter + 1)->flag.exec = true;
                    else
                        exec_flag = ePROCESS_COMP;
                    //                emit stepStatusSignal(st_task_status_->cur_step, st_task_status_->cur_status); //
                    break;
                default:
                    break;
                }

                exec_status                     = exec_flag;
                st_task_status_->cur_ack_status = exec_flag;
                return exec_status;
            }

            //* 单任务时间计数
#if 1
            if (st_task_status_->is_in_cycle) {
                if (T(iter - v_task_list_->begin()) == st_task_status_->cur_step) {  //用计数判断
                    // qDebug() << "-i process/ step:" << st_task_status_->cur_step << "time cnt:" << iter->time_cnt;
                    iter->time_cnt++;
                    if ((iter->task_limit_time != 0) && (iter->time_cnt * TIMER_MS) > iter->task_limit_time) {
                        exec_status = eFATAL;
                        //                emit stepStatusSignal(st_task_status_->cur_step, eFATAL);
                        //                    qDebug() << "-i process/ step:" << st_task_status_->cur_step << "timeout:" << iter->time_cnt;
                    }

                    if ((iter->ack_limit_time != 0) && (iter->time_cnt * TIMER_MS > iter->ack_limit_time)) {
                        LOG_WARN(MyLogger::LogType::PROCESS_STATUS, QString("%1 task ack timeout: %2").arg(st_task_status_->cur_step).arg(iter->reexec_cnt));
                        if (iter->reexec_cnt++ < iter->reexec_times) {  // re exect
                            iter->flag.exec = true;
                        } else {
                            exec_status = eFATAL;
                        }
                    }
                }
            }
#else  //定时器判断
            if (st_task_status_->step_timer.elapsed() > iter->interval_time) {
                emit stepStatusSignal(st_task_status_->cur_step, eFATAL);
                qDebug() << "-i clen/ step:" << st_task_status_->cur_step << "timeout:" << st_task_status_->step_timer.elapsed();
            }
#endif
        }
        return exec_status;
    }

    ParaType Result(ClassType *pClassType, fPtr fun_, ParaType a) {
        return (pClassType->*fun_)(a);
    }
};

#endif  // PROCESSLISTB_H
