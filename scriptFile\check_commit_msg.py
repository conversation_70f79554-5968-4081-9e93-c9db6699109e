'''
Author: 
Date: 
LastEditors: 
LastEditTime: 
Description: 检查commit msg是否符合 angular 标准
FilePath: ..\scriptFile\check_commit_msg.py
'''
#!/usr/bin/env python
# -*- coding: utf-8 -*-
import re
import sys
import os
import configparser
import subprocess

#*******************************
#不同项目变更变量:
# 1. 子程序目录
# 2. 
#*******************************
SUB_PROGRAM_FOLDER_NAME = "components\\"
SCRIPT_FOLDER_NAME = "scriptFile" # 脚本文件目录
GENERATE_LOG_SCRIPT_FILE_NAME = os.path.join(SCRIPT_FOLDER_NAME, "generate_changelog.py")
SYNC_MANUAL_SCRIPT_FILE_NAME = os.path.join(SCRIPT_FOLDER_NAME, "sync_changelog_to_manual.py")
#****************************

COMMIT_MSG_FILE = sys.argv[1]

# 运行程序合集 target_lists: main(LA) 主程序(非子程序，都算在主程序); cleansAdjust; comCheck; corePerformanceAnalysis; functionTest; motorMonitor; novaCalibration
TARGET_LISTS = ['master','lensAdjust', 'lensAdjust_MEMD', 'lensAdjust_rework', 'comCheck','motorMonitor','novaCalibration']
# 提交消息的规范正则表达式
COMMIT_MSG_REGEX = r'^\[(' + '|'.join(TARGET_LISTS) + r')\](feat|fix|refactor|docs|style|test|chore)(\([\w\-]+\))?: .{1,50}'

def read_commit_message(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        return file.read().strip()
    
def read_config(config_file):
    config = configparser.ConfigParser()
    config.read(config_file)

    required_options = ["log_file_path"]
    missing_options = [opt for opt in required_options if not config.has_option("LOG", opt)]
    
    if missing_options:
        raise ValueError(f"Missing required options in config file: {', '.join(missing_options)}")
    
    return {
        "log_file_path": config.get("LOG", "log_file_path")
    }

def write_config(config_file):
    config = configparser.ConfigParser()
    config.read(config_file)

    required_options = ["fw_type"]
    missing_options = [opt for opt in required_options if not config.has_option("PROJECT_VER", opt)]
    
    if missing_options:
        raise ValueError(f"Missing required options in config file: {', '.join(missing_options)}")
    
    # 写入新的值到 fw_type
    config.set("PROJECT_VER", "fw_type", "release")
    
    # 将修改后的配置写回文件
    with open(config_file, 'w') as configfile:
        config.write(configfile)

    return {
        "fw_type": config.get("PROJECT_VER", "fw_type")
    }

def check_commit_format(commit_msg):
    match = re.match(COMMIT_MSG_REGEX, commit_msg)
    if not match:
        # Detailed error message
        program_match = re.match(r'^\[(.*?)\]', commit_msg)
        if not program_match or program_match.group(1) not in TARGET_LISTS:
            return False, "Program identifier is incorrect or missing."
        
        type_match = re.match(r'^\[.*?\](feat|fix|refactor|docs|style|test|chore)', commit_msg)
        if not type_match:
            return False, "Commit type is incorrect or missing."
        
        scope_match = re.match(r'^\[.*?\](feat|fix|refactor|docs|style|test|chore)(\([\w\-]+\))?', commit_msg)
        if not scope_match:
            return False, "Commit scope is incorrect or missing."
        
        message_match = re.match(r'^\[.*?\](feat|fix|refactor|docs|style|test|chore)(\([\w\-]+\))?: .{1,50}', commit_msg)
        if not message_match:
            return False, "Commit message is incorrect or missing."
    
    return True, "Commit message format is correct."

if __name__ == "__main__":
    # print("Executing check_commit_msg.py")
    script_dir = os.path.dirname(os.path.abspath(__file__))
    # print(f"script_dir: {script_dir}")  # 添加调试信息
    project_dir = os.path.dirname(script_dir)
    config_path = os.path.join(script_dir, 'config.ini')

    # 读取项目版本默认参数
    # config = read_config(COMMIT_  MSG_FILE)
    # print(f"project dir: {project_dir}")  # 添加调试信息

    commit_message = read_commit_message(COMMIT_MSG_FILE)

    # 先检查 commit msg是否符合规范，有异常直接退出
    # is_valid, error_message = check_commit_format(commit_message)
    # if not is_valid:
    #     print(f"Commit message is incorrect: {error_message}")
    #     # print(f"Invalid commit message: {commit_message}")
    #     sys.exit(1)
    
    # Organize commit messages by program
    commit_dict = {program: [] for program in TARGET_LISTS}
    for line in commit_message.split('\n'):
        # 检查 commit msg是否符合规范，有异常直接退出
        is_valid, error_message = check_commit_format(line)
        if not is_valid:
            print(f"Commit message is incorrect: {error_message}")
            print(f"Invalid commit message: {line}")
            sys.exit(1)

        for program in TARGET_LISTS:
            if f"[{program}]" in line or f"[{TARGET_LISTS[0]}]" in line:
                only_message = re.sub(r'^\[.*?\]', '', line).strip() #去除program 名称
                commit_dict[program].append(only_message)
                print(f"{program} cache messages : {only_message}")
                # break

    changelog_updated = False
    for program, messages in commit_dict.items():
        if messages:
            if program != TARGET_LISTS[0]: 
                program = SUB_PROGRAM_FOLDER_NAME + program
            else: 
                program = ""
            program_directory = os.path.join(os.getcwd(), program)
            if not os.path.exists(program_directory):
                print(f"Program directory {program_directory} does not exist.") 
                sys.exit(1)
                # os.makedirs(program_directory)
            print(f"Commit messages for {program}: {messages}")
            subprocess.run([sys.executable, GENERATE_LOG_SCRIPT_FILE_NAME, program_directory] + messages, check=True)
            changelog_updated = True
    
    # After updating CHANGELOGs, sync changes to manual files
    if changelog_updated:
        try:
            print("Syncing CHANGELOG updates to manual files...")
            subprocess.run([sys.executable, SYNC_MANUAL_SCRIPT_FILE_NAME], check=True)
        except Exception as e:
            print(f"Warning: Failed to sync changelog to manual: {e}")
            
    write_config(config_path)        
    sys.exit(0)