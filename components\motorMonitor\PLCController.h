#ifndef PLCCONTROLLER_H
#define PLCCONTROLLER_H

#include <QObject>
#include <QSerialPort>
#include <QTimer>
#include <QByteArray>
#include <QDebug>
#include "IComm.h"

/**
 * @brief PLC控制器类
 * 用于接收和解析PLC发送的modbus RTU启停指令
 * 协议格式：设备地址(1字节) + 功能码(1字节) + 寄存器地址(2字节) + 数据(2字节) + CRC16(2字节)
 * 启动指令：01 06 00 01 00 01 [CRC16]
 * 停止指令：01 06 00 01 00 00 [CRC16]
 */
class PLCController : public QObject
{
    Q_OBJECT

public:
    explicit PLCController(QObject *parent = nullptr);
    ~PLCController();

    /**
     * @brief 设置PLC通信接口
     * @param comm 通信接口指针
     */
    void setCommInterface(IComm *comm);

    /**
     * @brief 启动PLC监听
     * @return 成功返回true
     */
    bool startListening();

    /**
     * @brief 停止PLC监听
     */
    void stopListening();

    /**
     * @brief 获取PLC连接状态
     * @return 连接状态
     */
    bool isConnected() const;

    /**
     * @brief 设置设备地址
     * @param address 设备地址(默认0x01)
     */
    void setDeviceAddress(uint8_t address);

private slots:
    /**
     * @brief 串口数据接收槽函数
     */
    void onDataReceived();

    /**
     * @brief 定时器超时处理
     */
    void onTimeout();

signals:
    /**
     * @brief PLC启动信号
     */
    void startCommandReceived();

    /**
     * @brief PLC停止信号
     */
    void stopCommandReceived();

    /**
     * @brief PLC连接状态变化信号
     * @param connected 连接状态
     */
    void connectionStatusChanged(bool connected);

    /**
     * @brief PLC通信错误信号
     * @param error 错误信息
     */
    void communicationError(const QString &error);

private:
    /**
     * @brief 解析接收到的modbus RTU数据
     * @param data 接收到的数据
     * @return 解析成功返回true
     */
    bool parseModbusData(const QByteArray &data);

    /**
     * @brief 计算CRC16校验码
     * @param data 数据
     * @param length 数据长度
     * @return CRC16值
     */
    uint16_t calculateCRC16(const uint8_t *data, int length);

    /**
     * @brief 验证CRC16校验码
     * @param data 完整数据包
     * @return 校验成功返回true
     */
    bool verifyCRC16(const QByteArray &data);

    /**
     * @brief 发送响应数据（可选）
     * @param originalCommand 原始命令
     */
    void sendResponse(const QByteArray &originalCommand);

    /**
     * @brief 重置接收缓冲区
     */
    void resetReceiveBuffer();

private:
    IComm *m_comm;                    // 通信接口
    QTimer *m_receiveTimer;           // 接收超时定时器
    QByteArray m_receiveBuffer;       // 接收缓冲区
    bool m_isListening;               // 监听状态
    uint8_t m_deviceAddress;          // 设备地址
    
    // Modbus RTU协议常量
    static const uint8_t FUNCTION_CODE_WRITE_SINGLE_REGISTER = 0x06;
    static const uint16_t CONTROL_REGISTER_ADDRESS = 0x0001;
    static const uint16_t START_COMMAND_VALUE = 0x0001;
    static const uint16_t STOP_COMMAND_VALUE = 0x0000;
    static const int MODBUS_RTU_MIN_LENGTH = 8;  // 最小数据包长度
    static const int RECEIVE_TIMEOUT_MS = 100;   // 接收超时时间
    
    // 日志标识
    static const QString LOG_PREFIX;
};

#endif // PLCCONTROLLER_H
