#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
Description: Sync CHANGELOG.md updates to the corresponding manual files
This script monitors changes in CHANGELOG.md files and syncs the new entries
to the corresponding manual files in the build/MinSizeRel/output/bin/manual directory.
'''

import os
import sys
import re
import datetime
import configparser
import shutil

# Constants for file paths
MANUAL_DIR = "build/MinSizeRel/output/bin/manual"
COMPONENTS_DIR = "components"
TARGET_LISTS = ['lensAdjust', 'lensAdjust_MEMD', 'lensAdjust_rework', 'comCheck', 'motorMonitor', 'novaCalibration']

def read_config():
    """Read configuration from config file"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    config_path = os.path.join(script_dir, 'config.ini')
    
    # Check if we should force a full sync
    if os.environ.get("FORCE_SYNC", "0") == "1":
        return {"last_sync_date": "1970-01-01", "last_synced_versions": {}}
    
    if not os.path.exists(config_path):
        print(f"Config file not found: {config_path}")
        return {}
    
    config = configparser.ConfigParser()
    config.read(config_path)
    
    if not config.has_section("SYNC"):
        config.add_section("SYNC")
        config.set("SYNC", "last_sync_date", datetime.datetime.now().strftime("%Y-%m-%d"))
        
    # Initialize the last synced versions dictionary
    last_synced_versions = {}
    for component in TARGET_LISTS:
        if config.has_option("SYNC", f"last_synced_version_{component}"):
            last_synced_versions[component] = config.get("SYNC", f"last_synced_version_{component}")
        else:
            last_synced_versions[component] = "0.0.0"
    
    with open(config_path, 'w') as configfile:
        config.write(configfile)
    
    return {
        "last_sync_date": config.get("SYNC", "last_sync_date", fallback="1970-01-01"),
        "last_synced_versions": last_synced_versions
    }

def update_config(last_sync_date, last_synced_versions):
    """Update last sync date and versions in config file"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    config_path = os.path.join(script_dir, 'config.ini')
    
    config = configparser.ConfigParser()
    if os.path.exists(config_path):
        config.read(config_path)
    
    if not config.has_section("SYNC"):
        config.add_section("SYNC")
    
    config.set("SYNC", "last_sync_date", last_sync_date)
    
    # Update the last synced versions for each component
    for component, version in last_synced_versions.items():
        config.set("SYNC", f"last_synced_version_{component}", version)
    
    with open(config_path, 'w') as configfile:
        config.write(configfile)

def extract_new_changelog_entries(changelog_path, last_synced_version):
    """Extract new entries from changelog file"""
    if not os.path.exists(changelog_path):
        return []
    
    with open(changelog_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find all version blocks with dates
    version_blocks = re.findall(r'## \[v([\d\.]+)\] - (\d{4}-\d{2}-\d{2})([\s\S]*?)(?=\n## \[|$)', content)
    
    new_entries = []
    for version, date, entry_content in version_blocks:
        # Compare with last synced version using version numbers
        if compare_versions(version, last_synced_version) > 0:
            # Clean up the entry content
            cleaned_content = entry_content.strip()
            new_entries.append({
                "version": version,
                "date": date,
                "content": cleaned_content
            })
    
    # Sort entries by version to get the newest first
    new_entries.sort(key=lambda x: x['version'], reverse=True)
    
    # Only return the newest entry
    return new_entries[:1] if new_entries else []

def compare_versions(version1, version2):
    """Compare two version strings. Returns 1 if version1 > version2, 0 if equal, -1 if version1 < version2"""
    v1_parts = list(map(int, version1.split('.')))
    v2_parts = list(map(int, version2.split('.')))
    
    # Pad with zeros if needed
    while len(v1_parts) < 3:
        v1_parts.append(0)
    while len(v2_parts) < 3:
        v2_parts.append(0)
    
    for i in range(3):
        if v1_parts[i] > v2_parts[i]:
            return 1
        elif v1_parts[i] < v2_parts[i]:
            return -1
    
    return 0

def format_content_for_manual(content, component_name, is_main_project=False):
    """Format content to match the manual style"""
    # Split the content into lines
    lines = [line.strip() for line in content.split('\n') if line.strip()]
    
    # For main project updates, first process the content without prefix
    if is_main_project:
        # Format each line as a numbered list with prefixes removed
        processed_lines = []
        for i, line in enumerate(lines, 1):
            # Remove bullet points if present
            if line.startswith('-') or line.startswith('*'):
                line = line[1:].strip()
            
            # Remove type prefixes like feat:, fix:, etc.
            type_match = re.match(r'(\w+)(?:\([\w\-]+\))?: (.*)', line)
            if type_match:
                # Just use the description part after the colon
                line = type_match.group(2)
            
            # For the first item, add the main project update prefix
            if i == 1:
                processed_lines.append(f"{i}. 主项目更新: {line}")
            else:
                processed_lines.append(f"{i}. {line}")
        
        return '<br>'.join(processed_lines)
    
    # Check if the content is already in numbered format
    if any(line.startswith(str(i) + '.') for i in range(1, 10) for line in lines):
        # Already in numbered format, just join with <br>
        return '<br>'.join(lines)
    
    # Convert bullet points to numbered list
    if all(line.startswith('-') or line.startswith('*') for line in lines):
        numbered_lines = []
        for i, line in enumerate(lines, 1):
            # Remove the bullet point
            text = line[1:].strip()
            
            # Extract just the description part (removing feat/fix/etc prefixes)
            type_match = re.match(r'(\w+)(?:\([\w\-]+\))?: (.*)', text)
            if type_match:
                # Just use the description part after the colon
                description = type_match.group(2)
                formatted_line = f"{i}. {description}"
            else:
                # If not in conventional commit format, just number the line
                formatted_line = f"{i}. {text}"
            
            numbered_lines.append(formatted_line)
        
        return '<br>'.join(numbered_lines)
    
    # If not bullets or already numbered, just number each line
    numbered_lines = [f"{i}. {line}" for i, line in enumerate(lines, 1)]
    return '<br>'.join(numbered_lines)

def update_manual_file(manual_path, new_entries, component_name):
    """Update the manual file with new changelog entries"""
    if not os.path.exists(manual_path):
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(manual_path), exist_ok=True)
        # Create a basic manual file with table format if it doesn't exist
        with open(manual_path, 'w', encoding='utf-8') as f:
            f.write(f"# {component_name} Manual\n\n")
            f.write(f"| 文档版本 | 软件版本号  | 变更日期      | 变更内容                                    | 变更人 |\n")
            f.write(f"| ---- | ------ | --------- | --------------------------------------- | --- |\n")
            f.write(f"| 1.0  | V1.0.0 | {datetime.datetime.now().strftime('%Y/%m/%d')} | 首次发布版本 | x   |\n\n")
    
    with open(manual_path, 'r', encoding='utf-8') as f:
        manual_content = f.read()
    
    # Split the content into lines for line-by-line processing
    content_lines = manual_content.split('\n')
    
    # Check for existing versions to avoid duplicates - more thorough check
    # Look for specific version numbers in the table rows
    existing_versions = set()
    for line in content_lines:
        # Match version patterns like V2.1.0 or v1.3.4 in table rows
        if '|' in line and re.search(r'\|\s*\d+\.\d+\s*\|', line):
            version_match = re.search(r'[Vv](\d+\.\d+\.\d+|\d+\.\d+)', line)
            if version_match:
                existing_versions.add(version_match.group(1))
    
    print(f"Existing versions detected: {existing_versions}")
    
    # Filter out entries with versions that already exist in the document
    filtered_entries = []
    for entry in new_entries:
        if entry['version'] in existing_versions:
            print(f"Skipping duplicate version {entry['version']} for {component_name}")
            continue
        filtered_entries.append(entry)
    
    # If no entries remain after filtering duplicates, return early
    if not filtered_entries:
        print(f"No new entries to add for {component_name} after filtering duplicates")
        return False
    
    # Detect table format and structure
    table_format = detect_table_format(content_lines)
    if not table_format:
        print(f"No valid table format found in {manual_path}")
        return False
    
    print(f"Detected table format: {table_format['type']} with {len(table_format['headers'])} columns")
    
    # Get the last version number from the table
    version_matches = re.findall(r'\|\s*(\d+\.\d+)\s*\|', manual_content)
    last_version = float(version_matches[0]) if version_matches else 1.0
    for ver in version_matches:
        try:
            current_ver = float(ver)
            if current_ver > last_version:
                last_version = current_ver
        except:
            pass
    
    # Prepare the new table rows
    new_rows = []
    for entry in filtered_entries:
        # Increment the version
        last_version += 0.1
        formatted_version = f"{last_version:.1f}"
        
        # Format the date from YYYY-MM-DD to YYYY/MM/DD
        formatted_date = entry['date'].replace('-', '/')
        
        # Format the content for the table
        formatted_content = format_content_for_manual(entry['content'], component_name)
        
        # Create the new row based on the detected table format
        if table_format['type'] == 'standard':
            # Standard format with 5 columns: 文档版本 | 软件版本号 | 变更日期 | 变更内容 | 变更人
            new_row = f"| {formatted_version}  | V{entry['version']} | {formatted_date} | {formatted_content} | x  |"
        elif table_format['type'] == 'extended':
            # Extended format with 6 columns: 文档版本 | 功能 | 软件版本号 | 变更日期 | 变更内容 | 变更人
            new_row = f"| {formatted_version}  | {component_name} | V{entry['version']} | {formatted_date} | {formatted_content} | x  |"
        else:
            # Unknown format, use standard as fallback
            new_row = f"| {formatted_version}  | V{entry['version']} | {formatted_date} | {formatted_content} | x  |"
        
        new_rows.append(new_row)
    
    # Find the appropriate position to insert new rows
    insert_position = find_table_insert_position(content_lines, table_format)
    if insert_position < 0:
        print(f"Could not find appropriate position to insert new rows in {manual_path}")
        return False
    
    # Insert the new rows into the content
    modified_lines = content_lines[:insert_position + 1]
    modified_lines.extend(new_rows)
    if insert_position + 1 < len(content_lines):
        modified_lines.extend(content_lines[insert_position + 1:])
    
    # Rebuild the content
    modified_content = '\n'.join(modified_lines)
    
    # Write the modified content back to the file
    with open(manual_path, 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    return True

def detect_table_format(content_lines):
    """Detect the table format from the content lines"""
    # Find the first table header line
    header_line = None
    for i, line in enumerate(content_lines):
        if re.match(r'^\s*\|\s*文档版本\s*\|', line):
            header_line = line
            print(f"Found table header at line {i}: {line}")
            break
    
    if not header_line:
        print("No table header found")
        return None
    
    # Count the number of columns
    columns = header_line.split('|')
    # Remove empty first/last elements if present
    columns = [col.strip() for col in columns if col.strip()]
    print(f"Detected {len(columns)} columns in table: {columns}")
    
    # Determine the table type
    if '功能' in header_line:
        print("Detected extended table format with '功能' column")
        return {
            'type': 'extended',
            'headers': columns,
            'header_line': header_line
        }
    else:
        print("Detected standard table format")
        return {
            'type': 'standard',
            'headers': columns,
            'header_line': header_line
        }

def find_table_insert_position(content_lines, table_format):
    """Find the appropriate position to insert new rows"""
    # Find all table entry lines
    table_entries = []
    in_table = False
    separator_line = -1
    
    for i, line in enumerate(content_lines):
        if table_format['header_line'] in line:
            in_table = True
            print(f"Found table header at line {i}")
            continue
        
        if in_table and i > 0 and re.match(r'^\s*\|\s*----', line):
            separator_line = i
            print(f"Found table separator at line {i}")
            continue
        
        if in_table and re.match(r'^\s*\|\s*\d+\.\d+\s*\|', line):
            table_entries.append(i)
            print(f"Found table entry at line {i}: {line}")
        elif in_table and not re.match(r'^\s*\|', line) and line.strip():
            # First non-table line after the table
            in_table = False
            print(f"Found end of table at line {i}")
    
    if table_entries:
        # Insert after the last table entry
        insert_pos = max(table_entries)
        print(f"Will insert after line {insert_pos}: {content_lines[insert_pos]}")
        return insert_pos
    elif separator_line >= 0:
        # Insert after the separator line
        print(f"Will insert after separator line {separator_line}")
        return separator_line
    else:
        # Could not determine insert position
        print("Could not determine insert position")
        return -1

def sync_changelog_to_manual():
    """Main function to sync changelog entries to manual files"""
    # Read last sync date and versions from config
    config = read_config()
    last_sync_date = config.get("last_sync_date", "1970-01-01")
    last_synced_versions = config.get("last_synced_versions", {})
    
    # If force sync is enabled, show a message
    if os.environ.get("FORCE_SYNC", "0") == "1":
        print("Force sync mode enabled - syncing all changelog entries")
    
    # Get current date as string
    current_date = datetime.datetime.now().strftime("%Y-%m-%d")
    
    # Check if a specific component is targeted
    target_component = os.environ.get("TARGET_COMPONENT")
    
    # Check each component
    updates_made = False
    updated_versions = last_synced_versions.copy()
    
    components_to_check = [target_component] if target_component else TARGET_LISTS
    
    for component in components_to_check:
        if target_component and component != target_component:
            continue
        
        # Get the last synced version for this component
        last_synced_version = last_synced_versions.get(component, "0.0.0")
        
        # Component changelog path
        changelog_path = os.path.join(COMPONENTS_DIR, component, "CHANGELOG.md")
        # Manual file path - handle special case for lensAdjust
        if component == "lensAdjust":
            manual_file = "lensAdjust_manual.md"
        else:
            manual_file = f"{component}_manual.md"
        manual_path = os.path.join(MANUAL_DIR, component, manual_file)
        
        # Extract new changelog entries based on version comparison
        new_entries = extract_new_changelog_entries(changelog_path, last_synced_version)
        
        if new_entries:
            print(f"Found {len(new_entries)} new entries for {component}")
            # Update manual file
            if update_manual_file(manual_path, new_entries, component):
                updates_made = True
                print(f"Updated manual file: {manual_path}")
                # Update the last synced version for this component
                updated_versions[component] = new_entries[0]["version"]
    
    # Skip main project changelog processing as requested
    # The previous code for processing main project changelog is commented out below:
    """
    # Also check main project changelog
    main_changelog_path = "CHANGELOG.md"
    if os.path.exists(main_changelog_path):
        new_entries = extract_new_changelog_entries(main_changelog_path, last_sync_date)
        if new_entries:
            print(f"Found {len(new_entries)} new entries for main project")
            # Update all component manuals with main project changes
            for component in TARGET_LISTS:
                # Handle special case for lensAdjust
                if component == "lensAdjust":
                    manual_file = "lensAdjust_manual.md"
                else:
                    manual_file = f"{component}_manual.md"
                manual_path = os.path.join(MANUAL_DIR, component, manual_file)
                
                # Prepare entries with a note that these are main project changes
                modified_entries = []
                for entry in new_entries:
                    modified_entry = entry.copy()
                    # Format the content with a note that it's from the main project
                    modified_entry["content"] = format_content_for_manual(
                        modified_entry['content'], 
                        component, 
                        is_main_project=True
                    )
                    modified_entries.append(modified_entry)
                
                if update_manual_file(manual_path, modified_entries, component):
                    updates_made = True
                    print(f"Updated manual file with main changes: {manual_path}")
    """
    
    # Update config with current date and updated versions if any updates were made
    if updates_made:
        update_config(current_date, updated_versions)
        print(f"Updated last sync date to {current_date} and component versions")
    else:
        print("No new changelog entries found")
    
    return updates_made

if __name__ == "__main__":
    print("Starting changelog to manual sync...")
    
    # Ensure the manual directory exists
    if not os.path.exists(MANUAL_DIR):
        print(f"Manual directory not found: {MANUAL_DIR}")
        os.makedirs(MANUAL_DIR, exist_ok=True)
        print(f"Created manual directory: {MANUAL_DIR}")
    
    # Sync changelog to manual
    if sync_changelog_to_manual():
        print("Sync completed successfully")
        sys.exit(0)
    else:
        print("No updates were made")
        sys.exit(0) 