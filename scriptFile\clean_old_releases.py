#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
Description: Delete old release versions of the executable when a new release is built
'''

import os
import re
import sys
import glob
import shutil

def clean_old_releases(output_dir, program_prefix, project_name, current_version):
    """
    Delete old release versions of the executable with the same prefix and project name
    but different versions than the current one.
    
    Args:
        output_dir (str): Directory where executables are stored
        program_prefix (str): Prefix of the executable (e.g., 'CSPC')
        project_name (str): Name of the project (e.g., 'LIDAR_IA')
        current_version (str): Current version number (e.g., '1.4.2')
    """
    # Ensure output directory exists
    if not os.path.exists(output_dir):
        print(f"Output directory {output_dir} does not exist.")
        return
    
    # Pattern to match: PREFIX_PROJECT_vX.X.X_YYYYMMDD_release.exe
    pattern = f"{program_prefix}_{project_name}_v*_*_release.exe"
    current_pattern = f"{program_prefix}_{project_name}_v{current_version}_*_release.exe"
    
    # Get all matching files
    os.chdir(output_dir)
    all_files = glob.glob(pattern)
    current_files = glob.glob(current_pattern)
    
    # Extract version from filename
    version_pattern = re.compile(f"{program_prefix}_{project_name}_v([0-9]+\.[0-9]+\.[0-9]+)_.*_release\.exe")
    
    for file in all_files:
        # Skip current version files
        if file in current_files:
            continue
        
        match = version_pattern.match(file)
        if match:
            old_version = match.group(1)
            print(f"Deleting old release version: {file}")
            try:
                os.remove(file)
                print(f"Successfully deleted {file}")
            except Exception as e:
                print(f"Failed to delete {file}: {e}")

if __name__ == "__main__":
    if len(sys.argv) < 5:
        print("Usage: python clean_old_releases.py <output_dir> <program_prefix> <project_name> <current_version>")
        sys.exit(1)
    
    output_dir = sys.argv[1]
    program_prefix = sys.argv[2]
    project_name = sys.argv[3]
    current_version = sys.argv[4]
    
    clean_old_releases(output_dir, program_prefix, project_name, current_version) 