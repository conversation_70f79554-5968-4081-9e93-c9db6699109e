#include "faculaContext.h"
#include "QMessageBox"

#define MP_MERGE_TIMES   2  //光斑数据整合次数
#define DISCARD_PACK_NUM 0  // greymap 舍弃包数

CFaculaContext::CFaculaContext(const ISensorBoardFactory::StFaculaSensorInfo &facula_config) {
    st_facula_sensor_info = facula_config;
    m_map_data.map_matrix.resize(st_facula_sensor_info.sensor_info.ylens);  //

    //* 不判定，只显示
    mst_map_info.xlens            = st_facula_sensor_info.sensor_info.xlens;
    mst_map_info.ylens            = st_facula_sensor_info.sensor_info.ylens;
    mst_map_info.target_tf        = st_facula_sensor_info.target_tf;
    mst_map_info.sensor_direction = st_facula_sensor_info.sensor_direction;
    mst_map_info.facula_form      = st_facula_sensor_info.facula_form;
    mst_map_info.symm_type        = st_facula_sensor_info.symm_adjust_type;

    externalMapInfoUpdate();
    //* 选择判定光斑：原光斑/处理后光斑
    if (st_facula_sensor_info.expand_facula_map_adjust) {
        mi_facula_ = IFaculaFactory::getInstance().faculaAdjustCreate(facula_config.facula_type, mst_interpolation_map_info);
    } else {
        mi_facula_ = IFaculaFactory::getInstance().faculaAdjustCreate(facula_config.facula_type, mst_map_info);
    }
    m_map_interpolation_data.map_matrix.resize(mst_interpolation_map_info.ylens);  //
    for (uint i = 0; i < mst_interpolation_map_info.ylens; i++) {
        m_map_interpolation_data.map_matrix[i].resize(mst_interpolation_map_info.xlens);
    }


    if (!targetFaculaArea()) {
        //        QMessageBox::warning(this,"", "光斑中心配置异常");
    }
}

CFaculaContext::~CFaculaContext() {
    delete mi_facula_;
}

void CFaculaContext::varibleInit() {
    mi_facula_->variblesInit();
}

bool CFaculaContext::targetFaculaArea() {
    if ((st_facula_sensor_info.sensor_info.xlens < 3 || st_facula_sensor_info.sensor_info.ylens < 2) ||
        (st_facula_sensor_info.target_tf.ax >= st_facula_sensor_info.sensor_info.xlens ||
         st_facula_sensor_info.target_tf.ay >= st_facula_sensor_info.sensor_info.ylens)) {

        return false;
    }
    return true;
}

// void CFaculaContext::targetFaculaAreaShow(QTableWidget *map, IFaculaAdjust::StMapInfo *map_info_) {
//    /*4. 目标区域凸显 (0,0),(1,0),(0,1),(-1,0),(0,-1)*/
//    volatile const uint8_t xlen = map_info_->xlens, ylen = map_info_->ylens;
//    volatile const uint8_t x_tf = map_info_->target_tf.ax, y_tf = map_info_->target_tf.ay;
//    int8_t coordinate_x = 0, coordinate_y = 0;
//    uint8_t tip_num = 0;

//    /*释放内存*/
//    uint8_t item_lens = map_info_->table_item.length();
//    if(item_lens != 0) {
//        for(uint i = 0; i < item_lens; i++) {
//            if(map_info_->table_item.at(i) != nullptr)
//                delete map_info_->table_item.at(i);
//        }
//    }
//    map_info_->table_item.clear();

//    for (uint y = 0; y < ylen; y++) {
////            if(y > y_tf)
////                coordinate_y = y - y_tf;
////            else
//            coordinate_y = y_tf - y;
//            coordinate_y = coordinate_y>0? coordinate_y:(-coordinate_y);
//        for (uint x = 0; x < xlen; x++) {
////                if(x > x_tf)
////                    coordinate_x = x - x_tf;
////                else
//            coordinate_x = x_tf - x;
//            coordinate_x = coordinate_x>0? coordinate_x:(-coordinate_x);

//            QTableWidgetItem *item = new QTableWidgetItem();
//            item->setForeground(Qt::yellow); //darkred
//            item->setFont(QFont("Times", 20, QFont::Black)); //加粗
//            if((coordinate_x <= 1) && (coordinate_y <= 1) && ((coordinate_x + coordinate_y) <= 1)) {
//                item->setForeground(Qt::green);
//                item->setFont(QFont("Times", 26, QFont::Black)); //加粗
//            }
//            item->setTextAlignment(Qt::AlignHCenter | Qt::AlignVCenter);

//            /*2. MP提示值*/
//            if(st_facula_sensor_info.sensor_info.order == ITable::EMpOrder::left_2_right) //左往右
//                tip_num = y*xlen + x + 1;
//            else
//                tip_num = x*ylen + y + 1;
//            item->setToolTip(QString::number(tip_num)); //提示 VI4300
//            map_info_->table_item.push_back(item);
//            map->setItem(y, x, item);
//        }
//    }
//}

// void CFaculaContext::greyMapShow(QTableWidget *map, IFaculaAdjust::StMapInfo *map_info_, const IFaculaAdjust::StMapData &map_data)
//{
//    uint8_t alpha_tmp = 180, rgb_tmp;
//    uint32_t data_tmp = 0;

//    uint8_t xlens = map_info_->xlens;
//    uint8_t ylens = map_info_->ylens;
//    uint32_t data_max = map_data.max_peak>10?map_data.max_peak:10; //peak 最小值

//    for (int y = 0; y < ylens; ++y)
//    {
//        for (int x = 0; x < xlens; ++x)
//        {
//            /*1. 数值 update*/
//            data_tmp = map_data.map_matrix.at(y).at(x);
//            map_info_->table_item.at(y*xlens + x)->setText(QString::number(data_tmp));

//            /*3. 背景颜色*/
//            QColor color;
//            rgb_tmp = 255 - (data_tmp * 255 / data_max);
//            alpha_tmp = data_tmp * 255 / data_max;
//            color.setRgb(rgb_tmp, rgb_tmp, rgb_tmp, alpha_tmp);
//            map->item(y,x)->setBackground(QBrush(color));
//        }
//    }
//}

void CFaculaContext::mergeDataClean() {
    mst_merge_data.map_data_cache.clear();
    mst_merge_data.merge_data_cnt = 0;
}

/**
 * @brief: 数据合并，均值，中值
 * @param: 原值，合并个数，处理方式
 * @result: 处理结果
 */
QVector<uint32_t> CFaculaContext::dataMerge(const QVector<uint32_t> &origin_data, const uint8_t &times, const uint8_t &types) {
    uint32_t          data_tmp = 0;
    QVector<uint32_t> data;
    uint8_t           num;
    if (times == 0)
        return data;
    num = origin_data.length() / times;
    if ((num * times) != origin_data.length())
        return data;
    if (types == 1) {
        for (uint16_t n = 0; n < num; ++n) {
            for (uint8_t j = 0; j < times; ++j)
                data_tmp += origin_data.at(j * num + n);
            data_tmp = data_tmp / times;
            data.push_back(data_tmp);
            data_tmp = 0;
        }
    }
    return data;
}

/**
 * @brief 数据合并处理
 * @return
 */
bool CFaculaContext::mapDataMerge(QByteArray &mp_origin_byte, StMergeData *merge_data_, QVector<uint32_t> *map_data_) {
    uint16_t num = mp_origin_byte.length() >> 2;

    if (merge_data_->merge_data_cnt >= DISCARD_PACK_NUM) {  //舍弃前几包
        for (uint16_t n = 0; n < num; ++n) {                //
            uint32_t data_tmp = (uchar)mp_origin_byte.at((n << 2) + 0) | ((uchar)mp_origin_byte.at((n << 2) + 1) << 8) |
                                ((uchar)mp_origin_byte.at((n << 2) + 2) << 16) | ((uchar)mp_origin_byte.at((n << 2) + 3) << 24);

            merge_data_->map_data_cache.push_back(data_tmp);  //
        }
    }

    merge_data_->merge_data_cnt++;
    mp_origin_byte.clear();

    if (merge_data_->merge_data_cnt == (MP_MERGE_TIMES + DISCARD_PACK_NUM)) {             // MP_MERGE_TIMES
        *map_data_ = dataMerge(merge_data_->map_data_cache, (uint8_t)MP_MERGE_TIMES, 1);  //

        merge_data_->merge_data_cnt = 0;
        merge_data_->map_data_cache.clear();

        return true;
    }

    return false;
}


/**
 * @brief CLenAdjustOpt::originDataHanlde
 * @param mp_origin_bytes -> 光斑矩阵数据
 * @return
 */
bool CFaculaContext::originDataHanlde(QByteArray &mp_origin_bytes) {
    uint16_t          num  = mp_origin_bytes.length() >> 2;
    uint8_t           ylen = st_facula_sensor_info.sensor_info.ylens;
    uint8_t           xlen = st_facula_sensor_info.sensor_info.xlens;
    QVector<uint32_t> map_data;  //

    if ((num == 0) || (num != (ylen * xlen))) {
        mergeDataClean();
        return false;
    }
    // 判断数据有效性
    //    if((num > 0) && num != m_cell_num_first) {//单元格更新
    //        m_cell_num_first = num;

    //        mergeDataClean();
    ////        merge_data_cnt = 0;
    ////        m_map_data_cache.clear();
    //        if(!cellNumUpdate(num)) return false; //
    //    }

    if (mapDataMerge(mp_origin_bytes, &mst_merge_data, &map_data)) {  //数据合并完成
        if (map_data.length() != ylen * xlen) {
            qDebug() << "-e clen/ map data length error" << map_data.length() << ylen * xlen;
            return false;
        }

        //* 添加数据,
        if (st_facula_sensor_info.sensor_info.order == ITable::EMpOrder::left_2_right) {  //光斑顺序 左->右 YC
            for (int y = 0; y < ylen; ++y) {
                m_map_data.map_matrix[y].clear();
                for (int x = 0; x < xlen; ++x) {
                    m_map_data.map_matrix[y].push_back(map_data.at((y * xlen) + x));
                }
            }
        } else {  //上->下 4300 4302
            for (int i = 0; i < ylen; ++i) {
                m_map_data.map_matrix[i].clear();  //;
            }
            for (int x = 0; x < xlen; ++x) {
                for (int y = 0; y < ylen; ++y) {
                    m_map_data.map_matrix[y].push_back(map_data.at((x * ylen) + y));
                }
            }
        }
        return true;
    }
    return false;
}

bool CFaculaContext::faculaHandle(uint16_t &                           adjust_status,
                                  QByteArray &                         facula_origin_data,
                                  const C3dHandMachine::St3D<int16_t> &move_delta_step,
                                  C3dHandMachine::St3D<int16_t> *      move_dis_) {
    //* 循环接收->数据处理  原始数据->处理后的数据
    if (originDataHanlde(facula_origin_data)) {
        mi_facula_->findMaxMP(&m_map_data);
        //        emit dataAckSignal();

        m_my_interPolation.bilinear_interpolation(m_map_data.map_matrix, m_map_interpolation_data.map_matrix);  //
        mi_facula_->findMaxMP(&m_map_interpolation_data);

        if (st_facula_sensor_info.expand_facula_map_adjust) {
            adjust_status = mi_facula_->faculaAdjust(&m_map_interpolation_data, move_delta_step, move_dis_);
        } else {
            adjust_status = mi_facula_->faculaAdjust(&m_map_data, move_delta_step, move_dis_);
        }
        //                qDebug() << "-i clen/ adjust code:" << adjust_status.adjust_step;
        //                qDebug() << "-i clen/ move distance:" << mst_move_distance_->x << mst_move_distance_->y << mst_move_distance_->z;

        return true;
    } else
        return false;  //
}

//* 调节数据
void CFaculaContext::externalMapInfoUpdate() {
    uint8_t ylens = st_facula_sensor_info.sensor_info.ylens;
    uint8_t xlens = st_facula_sensor_info.sensor_info.xlens;

    /*1.2 拓展表格配置*/
    mst_interpolation_map_info.xlens = xlens + (xlens % 2 == 0 ? 1 : 2);
    mst_interpolation_map_info.ylens = ylens + (ylens % 2 == 0 ? 1 : 2);

    mst_interpolation_map_info.target_tf.ax = st_facula_sensor_info.target_tf.ax + 1;
    mst_interpolation_map_info.target_tf.ay = st_facula_sensor_info.target_tf.ay + 1;

    mst_map_info.sensor_direction = st_facula_sensor_info.sensor_direction;
    mst_map_info.facula_form      = st_facula_sensor_info.facula_form;
    //    mst_interpolation_map_info.table_item.resize(0);
}

IFaculaAdjust::StMapInfo CFaculaContext::getExpandMapInfo() {
    return mst_interpolation_map_info;
}

IFaculaAdjust::StMapData CFaculaContext::getOriginMapData() {
    return m_map_data;
}

IFaculaAdjust::StMapData CFaculaContext::getExpandMapData() {
    return m_map_interpolation_data;
}
// void CFaculaContext::targetMapUpdate(const IFaculaAdjust::StMapTargetInfo &target_map) {
//    mi_facula_->targetMapUpdate(target_map);
//}

// uint8_t CFaculaContext::faculaAdjust(IFaculaAdjust::StMapData *map_data_,
//                                     const C3dHandMachine::St3D<int16_t> &move_delta_step, C3dHandMachine::St3D<int16_t> *move_dis_) {

//    return mi_facula_->faculaAdjust(map_data_, move_delta_step, move_dis_);
//}

/**
 * @brief CFaculaContext::faculaTest
 * @param facula_origin_data
 * @param adjust_status
 * @param target_facula
 * @param is_auto_adjust  自动调节
 * @return
 */
bool CFaculaContext::faculaTest(QByteArray &                           facula_origin_data,
                                uint16_t &                             adjust_status,
                                QVector<uint32_t> &                    target_facula,
                                const IFaculaAdjust::EFaculaJudgeMode &test_mode,
                                const bool &                           is_auto_adjust) {
    if (originDataHanlde(facula_origin_data)) {
        mi_facula_->findMaxMP(&m_map_data);
        adjust_status = mi_facula_->faculaTest(&m_map_data, target_facula, test_mode, is_auto_adjust);
        return true;
    } else {
        return false;
    }
}

bool CFaculaContext::localFaculaTest(const QVector<QVector<uint32_t>> &     map_matrix,
                                     uint16_t &                             adjust_status,
                                     QVector<uint32_t> &                    target_facula,
                                     const IFaculaAdjust::EFaculaJudgeMode &test_mode) {
    IFaculaAdjust::StMapData map_data;
    map_data.map_matrix = map_matrix;

    mi_facula_->findMaxMP(&map_data);
    adjust_status = mi_facula_->faculaTest(&map_data, target_facula, test_mode, false);
    return true;
}

QVector<QVector<uint32_t>> CFaculaContext::getMap() {
    return m_map_data.map_matrix;
}

// void CFaculaContext::findMaxMP(IFaculaAdjust::StMapData *map_data_) {
//    mi_facula_->findMaxMP(map_data_);
//}
