#include "motormonitorwidget.h"
#include "ui_motormonitorwidget.h"
#include <QMessageBox>

#include "itemFlowBottom.h"
#include "mywidget.h"
#include <QShortcut>


// const QString motorMonitorWidget::m_motor_version = "MotorSpeed_V1.5"; //

motorMonitorWidget::motorMonitorWidget(QWidget *parent)
    : QDockWidget(parent), ui(new Ui::motorMonitorWidget), mst_config_(new NMotorMonitor::StUiConfig), m_operation_(new CMotorMonitorOperation(*mst_config_)) {
    ui->setupUi(this);
    this->setWindowTitle(MOTOR_MONITOR_APP_WINDOW_TITLE);

    //* 1.0 界面初始化
    motorMonitorPlot = new motorMonitorCustPlot(*ui->motorPlot);

    //* 1.1 配置初始化
    ui->monitorMode->setCurrentIndex(2);
    ui->monitorTime->setCurrentIndex(1);                                  // 3s
    ui->monitorCycle->setCurrentIndex(1);                                 // 100圈
    ui->motorID->setValidator(new QRegExpValidator(QRegExp("[0-9]+$")));  //编号只能input 数字

    ui->standard->setCurrentIndex(1);
    ui->PWMRadio->setAlignment(Qt::AlignLeft);
    ui->motorID->setAlignment(Qt::AlignLeft);
    ui->biasRange->setAlignment(Qt::AlignLeft);

    //* 1.2statusBar 初始化
    //    ui->statusLayout->addWidget(m_statusBar);
    ui->statusMonitor->setStyleSheet(CItemFlowBottom::m_grey_SheetStyle);
    ui->statusData->setStyleSheet(CItemFlowBottom::m_grey_SheetStyle);
    ui->statusComp->setStyleSheet(CItemFlowBottom::m_grey_SheetStyle);
    ui->statusMonitorText->setFont(QFont("黑体", 8, QFont::Medium));
    ui->statusDataText->setFont(QFont("黑体", 8, QFont::Medium));
    ui->statusCompText->setFont(QFont("黑体", 8, QFont::Medium));

    //* 2.数据初始化

    //* 3.信号与槽
    QShortcut *temp = new QShortcut(this);
    //设置键值，也就是设置快捷键.
    //    temp->setKey(tr("ctrl+space"));
    temp->setKey(Qt::CTRL + Qt::Key_Space);
    //这个成员函数挺关键的，设置是否会自动反复按键.也就是说，当你一直按住键盘ctrl+空格时，会一直不停的调用对应的槽函数.
    temp->setAutoRepeat(false);
    //连接信号与槽，showSlot()是自定义的槽函数!
    connect(temp, SIGNAL(activated()), this, SLOT(on_startButton_clicked()));
    // connect(ui->startButton, &QPushButton::clicked, m_operation_->m_serial_thread_, &CMotorMonitorSerial::loop);
    // connect(m_operation_->m_sub_thread_, &QThread::finished, m_operation_->m_serial_thread_, &QObject::deleteLater);
    connect(ui->plcPortBox, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &motorMonitorWidget::on_devPortBox_currentIndexChanged);
    connect(m_operation_, &CMotorMonitorOperation::portUpdateSignals, this, &motorMonitorWidget::portListShow);             //列表更新
    connect(m_operation_, &CMotorMonitorOperation::devicePortUpdateSignal, this, &motorMonitorWidget::devicePortListShow);  //列表更新
    connect(m_operation_, &CMotorMonitorOperation::readySignal, this, &motorMonitorWidget::readyShow);                      // ready
    connect(m_operation_, &CMotorMonitorOperation::startAckSignal, this, &motorMonitorWidget::startAckShow);                // start ack
    connect(m_operation_, &CMotorMonitorOperation::dataAckSignal, this, &motorMonitorWidget::dataAckShow);                  // data ack
    connect(m_operation_, &CMotorMonitorOperation::compAckSignal, this, &motorMonitorWidget::compAckShow);                  // complete ack
    connect(m_operation_, &CMotorMonitorOperation::resultSignal, this, &motorMonitorWidget::resultShow);                    // result
}

motorMonitorWidget::~motorMonitorWidget() {
    delete ui;
    delete mst_config_;
    delete m_operation_;
}

void motorMonitorWidget::closeEvent(QCloseEvent *event) {
    Q_UNUSED(event);

    this->setAttribute(Qt::WA_DeleteOnClose);  //释放窗口资源
    QThread::msleep(50);
    emit motorMonitorCloseSiganl(true);
}

/**
 * @brief 更新窗口列表
 */
void motorMonitorWidget::portListShow(QStringList *port_list_, bool port_flag) {
    ui->portBox->blockSignals(true);  //必须需要阻塞信号  否则会发出不必要的信号触发回调  导致记录端口名出错

    ui->portBox->clear();                //会触发currentIndexChanged回调函数
    ui->portBox->addItems(*port_list_);  //会触发消息事件

    if (!port_flag)
        ui->portBox->setCurrentIndex(0);
    else
        ui->portBox->setCurrentText(mst_config_->cur_port_name);

    ui->portBox->blockSignals(false);
}

void motorMonitorWidget::devicePortListShow(QStringList *port_list_, bool port_flag) {
    ui->plcPortBox->blockSignals(true);  //必须需要阻塞信号  否则会发出不必要的信号触发回调  导致记录端口名出错

    ui->plcPortBox->clear();                //会触发currentIndexChanged回调函数
    ui->plcPortBox->addItems(*port_list_);  //会触发消息事件

    if (!port_flag)
        ui->plcPortBox->setCurrentIndex(0);
    else
        ui->plcPortBox->setCurrentText(mst_config_->cur_dev_port_name);

    ui->plcPortBox->blockSignals(false);
}

/**
 * @brief 配置更新
 */
void motorMonitorWidget::updateConfig(NMotorMonitor::StUiConfig *config_) {
    config_->port_name         = ui->portBox->currentText();
    config_->cur_port_name     = ui->portBox->currentText();
    config_->dev_port_name     = ui->plcPortBox->currentText();
    config_->cur_dev_port_name = ui->plcPortBox->currentText();

    config_->mode = ui->monitorMode->currentIndex();

    config_->pwm_radio       = ui->PWMRadio->text().toInt();
    config_->limit_time      = ui->monitorTime->currentText().toUInt();   //监测配置时间
    config_->limit_cycle_num = ui->monitorCycle->currentText().toUInt();  //

    config_->id         = ui->motorID->text().toInt();
    config_->judge_mode = (NMotorMonitor::EJudgeMode)ui->standard->currentIndex();

    config_->extreme_bias_stand       = ui->biasRange->text().toUInt();
    config_->standard_deviation       = ui->standInterval->text().toUInt();
    config_->standard_deviation_range = ui->standardRange->text().toFloat();
}


uint8_t motorMonitorWidget::getPwmRadio(void) {
    uint8_t pwm_radio = ui->PWMRadio->text().toInt();
    if (pwm_radio >= 100 || pwm_radio < 1)  //占空比过滤
    {
        QMessageBox::warning(this, tr("pwm radio数值错误"), tr("1-99"));
        if (pwm_radio >= 100)
            pwm_radio = 99;
        else
            pwm_radio = 1;

        ui->PWMRadio->setText(QString::number(pwm_radio));
    }
    return pwm_radio;
}

/**
 * @brief 任务开始
 */
void motorMonitorWidget::on_startButton_clicked() {
    if (ui->serialPushButton->text() == "close") {
        if (ui->startButton->text() == QString("start")) {
            // updateConfig(mst_config_);                                                         //配置更新
            m_operation_->mv_task_list[CMotorMonitorOperation::eREADY_STEP].flag.exec = true;  //开始任务
        } else {
            m_operation_->mv_task_list[CMotorMonitorOperation::eCOMPLETE_STEP].flag.exec = true;  //结束任务
        }
    } else {
        QMessageBox::information(this, "error", "pls open port first");
    }
}

void motorMonitorWidget::on_serialPushButton_clicked() {
    if (ui->serialPushButton->text() == "open") {
        updateConfig(mst_config_);  //配置更新

        if (mst_config_->port_name == mst_config_->dev_port_name) {
            QMessageBox::information(this, "error", "device port can't the same with motor port");
            return;
        }

        if (!m_operation_->serialPortSwitch(true)) {
            QMessageBox::information(this, "error", "port can't open");
            return;
        }

        ui->serialPushButton->setText("close");
    } else {  //关闭->退出
        m_operation_->serialPortSwitch(false);

        ui->serialPushButton->setText("open");
    }
}

/**
 * @brief 显示界面清空
 */
void motorMonitorWidget::resultClean(void) {
    //  ui->rpsMax->setText("");
    //  ui->rpsMin->setText("");
    ui->rpsMean->setText("");
    ui->indicator_one->setText("");
    ui->indicator_two->setText("");

    ui->monitorResult->setStyleSheet("color: black;");
    ui->monitorResult->setText("RESULT");
}

/**
 * @brief 执行状态更新
 * @param step
 * @param status
 */
void motorMonitorWidget::statusBarUpdate(CMotorMonitorOperation::EProcessStep step, EExecStatus status) {
    switch (step) {
    case CMotorMonitorOperation::eSTART_STEP:
        if (status == EExecStatus::eOK) {
            ui->statusMonitor->setStyleSheet(CItemFlowBottom::m_green_SheetStyle);
            ui->statusMonitorText->setText("start");
        }
        //        else if(status == ECommStatus::eCOMM_OK)
        //          {
        //            ui->statusMonitor->setStyleSheet(CItemFlowBottom::m_green_SheetStyle); //改成
        //            ui->statusMonitorText->setText("ok");
        //          }
        //        else if(status == ECommStatus::eCOMM_ERROR) //
        //          {
        //            ui->statusMonitor->setStyleSheet(CItemFlowBottom::m_red_SheetStyle); //改成
        //            ui->statusMonitorText->setText("error");
        //          }
        //        else if(status == ECommStatus::eCOMM_TIMEOUT)
        //          {
        //            ui->statusMonitor->setStyleSheet(CItemFlowBottom::m_red_SheetStyle);
        //            ui->statusMonitorText->setText("timeout");
        //          }
        //        break;
        //      case EProcessStep::eDATA_STEP:
        //        if(status == ECommStatus::eCOMM_OK)
        //          {
        //            ui->statusData->setStyleSheet(CItemFlowBottom::m_green_SheetStyle);
        //            ui->statusDataText->setText("recording");
        //          }
        //        else if(status == ECommStatus::eCOMM_ERROR)
        //          {
        //            ui->statusData->setStyleSheet(CItemFlowBottom::m_red_SheetStyle);
        //            ui->statusDataText->setText("error");
        //          }
        //        else if(status == ECommStatus::eCOMM_TIMEOUT)
        //          {
        //            ui->statusData->setStyleSheet(CItemFlowBottom::m_red_SheetStyle);
        //            ui->statusDataText->setText("timeout");
        //          }
        //        else if(status == ECommStatus::eCOMM_FATAL)
        //          {
        //            ui->statusData->setStyleSheet(CItemFlowBottom::m_red_SheetStyle);
        //            ui->statusDataText->setText("block");
        //          }
        //        break;
        //      case EProcessStep::eCOMPLETE_STEP:
        //        if(status == ECommStatus::eCOMM_COMP)
        //          {
        //            ui->statusComp->setStyleSheet(CItemFlowBottom::m_green_SheetStyle);
        //            ui->statusCompText->setText("complete");
        //          }
        //        else if(status == ECommStatus::eSTART)
        //          {
        //            ui->statusDataText->setText("stop");
        //          }
        break;
    default:
        break;
    }
}

/**
 * @brief 画布显示
 */
void motorMonitorWidget::plotShow() {
    motorMonitorPlot->customPlotShow(1, motorMonitorPlot->dataHandle(m_operation_->mst_motor_fg_data_->data));
    motorMonitorPlot->customPlotShow(
        2, motorMonitorPlot->dataHandle1((uint16_t)round((float)m_operation_->mst_motor_fg_data_->min / 10) - 1, m_operation_->mst_cycle_data_->distribute));
}

// void motorMonitorWidget::resultDataShow()
//{


//}

/**
 * @brief 结果显示
 * @param motor_data
 * @param result
 */
// void motorMonitorWidget::resultShow(const StMotorData &motor_data, const StResult &result)
void motorMonitorWidget::resultShow(uint16_t mean) {
    //* plot显示
    plotShow();
    //, uint16_t max_bias, uint16_t min_bias, bool result
    //* 结果显示
    ui->rpsMean->setText(QString::number(((float)mean / 100), 'f', 2));

#if 0  //根据判定标准只显示相应数据
    switch(mst_config_->judge_mode) {
    case NMotorMonitor::eEXTREME_MODE: //
        //  ui->rpsMax->setText(QString::number(((float)motor_data.max/100),'f',2)); //0.1ms
        //  ui->rpsMin->setText(QString::number(((float)motor_data.min/100),'f',2));
        ui->indicator_one->setText(QString::number(m_operation_->mst_result_->max_bias));
        ui->indicator_two->setText(QString::number(m_operation_->mst_result_->min_bias));
        break;
    case NMotorMonitor::eCONFIDENT_INTERVAL:

        break;
    case NMotorMonitor::eSTANDARD_DEVIATION:
        ui->indicator_one->setText(QString::number(m_operation_->mst_result_->max_bias));
        ui->indicator_two->setText(QString::number(m_operation_->mst_result_->min_bias));
//        ui->indicator_three->setText(QString::number(m_operation_->mst_result_->sigma));
        break;
    default:
        break;
    }
#else  //所有数据显示
    ui->indicator_one->setText(QString::number(m_operation_->mst_result_->max_bias));
    ui->indicator_two->setText(QString::number(m_operation_->mst_result_->min_bias));
    ui->sigma->setText(QString::number(m_operation_->mst_result_->sigma));
    ui->extremeRatio->setText(QString::number(m_operation_->mst_result_->extreme_ratio, 'f', 2));
#endif
    // if (m_operation_->mst_result_->result) {  //差值过大
    //     ui->monitorResult->setStyleSheet("color: green;");
    //     ui->monitorResult->setText("PASS");
    // } else {
    //     ui->monitorResult->setStyleSheet("color: red;");
    //     ui->monitorResult->setText("FAIL");  //
    // }
}


void motorMonitorWidget::readyShow(bool is_open) {
    if (is_open) {
        ui->startButton->setText("stop");
        resultClean();
    }
}

/**
 * @brief ack status update
 */
void motorMonitorWidget::startAckShow(EExecStatus status) {
    //  if(static_cast<uint8_t>(status) < 0)
    //    ui->startButton->setText("start");
    //  statusBarUpdate(CMotorMonitorOperation::eSTART_STEP, status);
}

/**
 * @brief 数据接收正常与异常 status update
 */
void motorMonitorWidget::dataAckShow(EExecStatus status) {
    //  if(static_cast<uint8_t>(status) < 0) //异常
    //    ui->startButton->setText("start");
    //  statusBarUpdate(CMotorMonitorOperation::eDATA_STEP, status);
}

/**
 * @brief
 */
void motorMonitorWidget::compAckShow(uint16_t status) {
    //  statusBarUpdate(CMotorMonitorOperation::eCOMPLETE_STEP, status);
    ui->startButton->setText("start");  //

    QString status_text = "";

    if (status == CMotorMonitorOperation::STATUS_PASS) {  //差值过大
        ui->monitorResult->setStyleSheet("color: green;");
        ui->monitorResult->setText("PASS");
    } else {
        if ((status & CMotorMonitorOperation::STATUS_TASK_ERR) == CMotorMonitorOperation::STATUS_TASK_ERR) {
            ui->monitorResult->setStyleSheet("color: red;");
            ui->monitorResult->setText("TASK ERR");  //
        } else if ((status & CMotorMonitorOperation::STATUS_DATA_ERR) == CMotorMonitorOperation::STATUS_DATA_ERR) {
            ui->monitorResult->setStyleSheet("color: red;");
            ui->monitorResult->setText("DATA ERR");  //
        } else if ((status & CMotorMonitorOperation::STATUS_PLC_ERR) == CMotorMonitorOperation::STATUS_PLC_ERR) {
            ui->monitorResult->setStyleSheet("color: red;");
            ui->monitorResult->setText("PLC ERR");  //
        } else {
            ui->monitorResult->setStyleSheet("color: red;");
            ui->monitorResult->setText("NG");  //
        }
    }
}

void motorMonitorWidget::on_standard_currentIndexChanged(int index) {
    //    switch (NMotorMonitor::EJudgeMode(index)) {
    //    case
    //    }
    ui->tabWidget->setCurrentIndex(index);

    //    switch(index){
    //    case 0: //
    //        ui->standardLabel->setText("偏差标准(‰):");
    //        ui->standardRange->setText("30");
    //        ui->standInterval->setEnabled(false);
    //        ui->offset1->setText("Vmax偏差/(‰):");
    //        ui->offset2->setText("Vmin偏差/(‰):");
    //        break;
    //    case 1:
    //        ui->standardLabel->setText("置信度(‰):");
    //        ui->standardRange->setText("95");
    //        ui->standInterval->setEnabled(true); //
    //        ui->offset1->setText("区间左:");
    //        ui->offset2->setText("区间右:");
    //        break;
    //    case 2:
    //        ui->standardLabel->setText("标准:");
    //        ui->standInterval->setEnabled(true); //
    //        ui->standardRange->setText("12");
    //        ui->offset1->setText("Vmax偏差/(‰):");
    //        ui->offset2->setText("Vmin偏差/(‰):");
    ////        ui->offset3->setText("标准差:");
    //        break;
    //    default:
    //        break;
    //    }
}

void motorMonitorWidget::on_portBox_currentIndexChanged(int index) {
    Q_UNUSED(index);
    mst_config_->cur_port_name = ui->portBox->currentText();
}

void motorMonitorWidget::on_devPortBox_currentIndexChanged(int index) {
    Q_UNUSED(index);
    mst_config_->cur_dev_port_name = ui->plcPortBox->currentText();
}
