## Introduction
The Nova calibration application is used for calibrating Nova products, verifying accuracy, and conducting function tests. Different products may have different versions and configuration files. Users need to modify the configuration files before using the application. Quality personnel should turn off calibration and other functions in the configuration files as needed.
## Getting started
1. get software
2. open software
    
    ![[/image 15.png|image 15.png]]
    
3. open “NOVA校正” function
    
    ![[/image 1 7.png|image 1 7.png]]
    
4. start test
    
    ![[/image 2 4.png|image 2 4.png]]
    
      
    
## Config file
- Nova-xx_config.ini
    
    nova device’s configurations
    
    - location: $PWD/config/device/pms
    - content:
- Nova-xxx_config.xml
    
    process configurations
    
    - location: $PWD/config/nova
    - content:
        
- Nova-xxx_verify_config.csv
    
    Verifying accuracy settings
    
    - location: $PWD/config/nova
    - content:
        
        ![[/image 3 4.png|image 3 4.png]]
        
- Nova-A3-045_test_function_config.csv
    
    Configuration of functions
    
    - location: $PWD/config/nova
    - content:
        
        ![[/image 4 2.png|image 4 2.png]]
        
## Data cache
- novaCalib_Nova-xxx.csv
    - location:
        - $PWD/
    - content
        
        ![[/image 5 2.png|image 5 2.png]]
        
- calib_verify_product_info.xml
    
    test count cache file
    
    - locaton: $PWD/config/nova
    - content
        
    - note:
        - if current device has same chip id with last, will not increment the count
        - Users need to manually add new device configurations. The project_name may be "Nova-A1K" if proj_ver_protocol is set to 0, or "Nova-A3-045" if proj_ver_protocol is set to 1.
            
            ```JavaScript
                <porject_name>
                    <batch_ok_num>0</batch_ok_num>
                    <!--  单批次正常数量   -->
                    <product_total_num>6</product_total_num>
                    <!--  总测试模组数   -->
                    <good_product_num>1</good_product_num>
                    <!--  合格品数   -->
                    <bad_product_num>5</bad_product_num>
                    <!--  不良品数   -->
                    <bad_product_rate>0.833</bad_product_rate>
                    <!--  不良率   -->
                    <process_time>13.9</process_time>
                    <!--  单次检测时长   -->
                    <aver_process_time>16.8</aver_process_time>
                    <!--  平均检测时长   -->
                </project_name>
            ```
            
        - The application will load current project’s production’s info after open “NOVA校正” function.
## Error info
- module dds info
    - A1
        
        ```C
        typedef union {
            uint16_t dds_info;
            struct {
                uint16_t iic_comm : 1;
                uint16_t V5300_init : 1;
                uint16_t integral_set : 1;
                uint16_t start_measure : 1;  // 4
                uint16_t get_distance : 1;
                uint16_t xtalk_calib : 1;
                uint16_t ref_tof : 1;
                uint16_t vi530x_config : 1;  // 8 校正参数
                uint16_t vi530x_IIC_DEV : 1;
                uint16_t voltage : 1;
                uint16_t flash_w : 1;
                uint16_t reserve0 : 1;
            } all_info;
        } UDdsInfo;
        ```
        
    - A2 || A3
        
        ```C
        typedef union {
            uint16_t dds_info;
            struct {
                uint16_t iic_comm : 1;
                uint16_t vi530x_init : 1;
                uint16_t VI530x_set_integral : 1;
                uint16_t VI530x_single_measure : 1;  // 4
                uint16_t VI530x_get_distance : 1;
                uint16_t vi530x_xtalk : 1;
                uint16_t vi530x_offset : 1;
                uint16_t vi530x_config : 1;  // 8 校正参数
                uint16_t vi530x_IIC_DEV : 1;
                uint16_t voltage : 1;  // vol_collect
                uint16_t fmc_err : 1;
                uint16_t uart_ack_busy : 1;
                uint16_t work_logic : 1;  // 12
                uint16_t reserve1 : 1;
                uint16_t reserve2 : 1;
            } all_info;
        } UDdsInfo;
        ```
        
- process error info
    
    |   |   |
    |---|---|
    |code|describe|
    |0x01|bar code error|
    |0x02|project name or version or date errror|
    |0x04|chip id error|
    |0x08|turntable move error|
    |0x10|turntable angle error|
    |0x20|verifying distance accuracy error|
    |0x40|internal voltage correct error|
    
- tasks running error info
    
## Running log
- location: $PWD/error.log
note:
- **Clear this file regularly**
## Update log
|   |   |   |   |   |
|---|---|---|---|---|
|user manual version|software version|date|content|author|
|1.0|V1.0.0|2024/10/11|首次发布版本|luo|
## Getting help
If you have any questions, contact us via @gmail.com