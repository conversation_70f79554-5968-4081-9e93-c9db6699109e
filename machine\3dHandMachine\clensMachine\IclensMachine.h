#ifndef _ICLEN_MACHINE_H_
#define _ICLEN_MACHINE_H_

#include "3dHandMachine.h"
#include "IComm.h"
#include "IDevicePtl.h"
#include <QByteArray>
#include <QMap>
#include <QMetaType>
#include <QObject>


class IClensMachine : public C3dHandMachine {
    Q_OBJECT
  public:
    IClensMachine();
    virtual ~IClensMachine(){};

    //操作模式
    enum EMode {
        auto_mode = 0,
        manual_mode,
    };

    //* 交互步骤
    enum ECommAck {
        eLEN_CAP_ACK,     //抓取指令ack
        eLEN_STATUS_ACK,  //镜片方式状态
        eLOC_ACK,         //

        eADJUST_LEN_ACK,
        eSOLID_ACK,
        eSOLID_STATUS_ACK,
        eDEFLATE_ACK,
        eEXIT_ACK,
    };

    enum class EDeviceStatus {
        eXY_AXIS_LMIT = 1 << 0,
        eZ_AXIS_LMIT  = 1 << 1,
        eCMD_SEND     = 1 << 6,  // cmd send or not
        eCMD_STATUS   = 1 << 7,  // 0-ERROR, 1-OK
    };

    QByteArray m_strPre;

    virtual void       icom_change_interface(IComm *port_)   = 0;
    virtual QByteArray portDataRead()                        = 0;  //读取缓存buffer数据
    virtual bool       start(const QVector<uint16_t> &data)  = 0;  //开始
    virtual bool       stop(const QByteArray &data)          = 0;  //停止
    virtual bool       getStatus(void)                       = 0;  // const uint16_t &addr
    virtual bool       getLoc(const uint8_t &dimension)      = 0;
    virtual bool       adjust(const St3D<int16_t> &move_dis) = 0;  //调节
    virtual bool       solid(void)                           = 0;
    virtual bool       deflate(void)                         = 0;

    virtual bool dataParsing(QByteArray str, const int &length) = 0;  //数据解析

    virtual void                          cleanMoveDistance(void) = 0;
    virtual C3dHandMachine::St3D<int16_t> getMoveStep(void)       = 0;
    virtual C3dHandMachine::St3D<int16_t> getMoveDist(void)       = 0;
    //  virtual inline int16_t move_dis_cal(const int8_t &mp_delta, const int16_t &peak_delta, const uint8_t &move_step) = 0;
    //  virtual bool deviceSpeedHandle(C3dHandMachine::St3D<int16_t> *move_dist, const C3dHandMachine::St3D<uint16_t> &limit_radius) = 0;
    virtual uint8_t deviceSpeedHandle(const C3dHandMachine::St3D<int16_t> &move_step, uint16_t &wait_times) = 0;

  protected:
    //* 设备端口
    IDevicePtl *m_protocol_ = nullptr;  //一个设备一种协议

    //* 设备指令
    QMap<QString, QByteArray> m_cmd = {
        {"start", {0}},
        {"stop", {0}},
        {"status", {0}},
        {"loc", {0}},
        {"axis", {0}},
        {"move", {0}},
        {"solid", {0}},
        {"deflate", {0}},
    };

    //* 设备信息 坐标
    St3D<double> m_origin_loc;  //
    St3D<double> m_final_loc;   //
    St3D<double> m_loc;         //

    //* 设备移动
    St3D<uint8_t> m_step_dis;  //步进

  public:
    St3D<int16_t>  mst_move_delta;      //移动距离计数
    St3D<uint16_t> last_move_delta;     //上一次总移动距离
    St3D<uint16_t> last_move_distance;  //上一次移动步进
    //  St3D<uint16_t>      peak_delta; //peak差值
};


#endif
