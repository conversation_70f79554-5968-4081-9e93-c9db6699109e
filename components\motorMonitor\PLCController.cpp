#include "PLCController.h"
#include <QDebug>

const QString PLCController::LOG_PREFIX = "[PLC]";

PLCController::PLCController(QObject *parent)
    : QObject(parent), m_comm(nullptr), m_receiveTimer(new QTimer(this)), m_isListening(false), m_deviceAddress(0x01) {
    // 配置接收定时器
    m_receiveTimer->setSingleShot(true);
    m_receiveTimer->setInterval(RECEIVE_TIMEOUT_MS);
    connect(m_receiveTimer, &QTimer::timeout, this, &PLCController::onTimeout);

    qDebug() << LOG_PREFIX << "PLC Controller initialized";
}

PLCController::~PLCController() {
    stopListening();
    qDebug() << LOG_PREFIX << "PLC Controller destroyed";
}

void PLCController::setCommInterface(IComm *comm) {
    if (m_isListening) {
        qWarning() << LOG_PREFIX << "Cannot change comm interface while listening";
        return;
    }

    m_comm = comm;
    qDebug() << LOG_PREFIX << "Communication interface set";
}

bool PLCController::startListening() {
    if (!m_comm) {
        qWarning() << LOG_PREFIX << "Communication interface not set";
        emit communicationError("Communication interface not set");
        return false;
    }

    if (!m_comm->checkPort()) {
        qWarning() << LOG_PREFIX << "Communication port not open";
        emit communicationError("Communication port not open");
        return false;
    }

    m_isListening = true;
    resetReceiveBuffer();

    // 启动定时器定期检查串口数据
    QTimer *checkTimer = new QTimer(this);
    connect(checkTimer, &QTimer::timeout, this, &PLCController::onDataReceived);
    checkTimer->start(50);  // 每50ms检查一次

    qDebug() << LOG_PREFIX << "Started listening for PLC commands";
    emit connectionStatusChanged(true);

    return true;
}

void PLCController::stopListening() {
    m_isListening = false;
    m_receiveTimer->stop();
    resetReceiveBuffer();

    qDebug() << LOG_PREFIX << "Stopped listening for PLC commands";
    emit connectionStatusChanged(false);
}

bool PLCController::isConnected() const {
    return m_isListening && m_comm && m_comm->checkPort();
}

void PLCController::setDeviceAddress(uint8_t address) {
    m_deviceAddress = address;
    qDebug() << LOG_PREFIX << "Device address set to:" << QString("0x%1").arg(address, 2, 16, QChar('0'));
}

void PLCController::onDataReceived() {
    if (!m_isListening || !m_comm) {
        return;
    }

    // 读取串口数据
    QByteArray newData = m_comm->read(10);  // 10ms超时
    if (newData.isEmpty()) {
        return;
    }

    // 添加到接收缓冲区
    m_receiveBuffer.append(newData);

    // 重启接收定时器
    m_receiveTimer->start();

    qDebug() << LOG_PREFIX << "Received data:" << newData.toHex(' ');
}

void PLCController::onTimeout() {
    if (m_receiveBuffer.length() >= MODBUS_RTU_MIN_LENGTH) {
        // 尝试解析数据
        if (parseModbusData(m_receiveBuffer)) {
            qDebug() << LOG_PREFIX << "Command parsed successfully";
        } else {
            qWarning() << LOG_PREFIX << "Failed to parse command:" << m_receiveBuffer.toHex(' ');
            emit communicationError("Invalid modbus command received");
        }
    } else if (!m_receiveBuffer.isEmpty()) {
        qWarning() << LOG_PREFIX << "Incomplete data received:" << m_receiveBuffer.toHex(' ');
        emit communicationError("Incomplete modbus command received");
    }

    // 清空缓冲区
    resetReceiveBuffer();
}

bool PLCController::parseModbusData(const QByteArray &data) {
    if (data.length() < MODBUS_RTU_MIN_LENGTH) {
        qWarning() << LOG_PREFIX << "Data too short for modbus RTU";
        return false;
    }

    const uint8_t *bytes = reinterpret_cast<const uint8_t *>(data.constData());

    // 检查设备地址
    uint8_t deviceAddr = bytes[0];
    if (deviceAddr != m_deviceAddress) {
        qDebug() << LOG_PREFIX << "Device address mismatch. Expected:" << QString("0x%1").arg(m_deviceAddress, 2, 16, QChar('0'))
                 << "Received:" << QString("0x%1").arg(deviceAddr, 2, 16, QChar('0'));
        return false;
    }

    // 检查功能码
    uint8_t functionCode = bytes[1];
    if (functionCode != FUNCTION_CODE_WRITE_SINGLE_REGISTER) {
        qWarning() << LOG_PREFIX << "Unsupported function code:" << QString("0x%1").arg(functionCode, 2, 16, QChar('0'));
        return false;
    }

    // 检查寄存器地址
    uint16_t regAddr = (bytes[2] << 8) | bytes[3];
    if (regAddr != CONTROL_REGISTER_ADDRESS) {
        qWarning() << LOG_PREFIX << "Invalid register address:" << QString("0x%1").arg(regAddr, 4, 16, QChar('0'));
        return false;
    }

    // 验证CRC16
    if (!verifyCRC16(data)) {
        qWarning() << LOG_PREFIX << "CRC16 verification failed";
        return false;
    }

    // 解析命令数据
    uint16_t commandValue = (bytes[4] << 8) | bytes[5];

    if (commandValue == START_COMMAND_VALUE) {
        qDebug() << LOG_PREFIX << "START command received";
        emit startCommandReceived();
        sendResponse(data);
        return true;
    } else if (commandValue == STOP_COMMAND_VALUE) {
        qDebug() << LOG_PREFIX << "STOP command received";
        emit stopCommandReceived();
        sendResponse(data);
        return true;
    } else {
        qWarning() << LOG_PREFIX << "Unknown command value:" << QString("0x%1").arg(commandValue, 4, 16, QChar('0'));
        return false;
    }
}

uint16_t PLCController::calculateCRC16(const uint8_t *data, int length) {
    uint16_t crc = 0xFFFF;

    for (int i = 0; i < length; i++) {
        crc ^= data[i];
        for (int j = 0; j < 8; j++) {
            if (crc & 0x0001) {
                crc = (crc >> 1) ^ 0xA001;
            } else {
                crc >>= 1;
            }
        }
    }

    return crc;
}

bool PLCController::verifyCRC16(const QByteArray &data) {
    if (data.length() < MODBUS_RTU_MIN_LENGTH) {
        return false;
    }

    const uint8_t *bytes      = reinterpret_cast<const uint8_t *>(data.constData());
    int            dataLength = data.length() - 2;  // 除去CRC16的长度

    // 计算CRC16
    uint16_t calculatedCRC = calculateCRC16(bytes, dataLength);

    // 提取接收到的CRC16 (小端序)
    uint16_t receivedCRC = bytes[dataLength] | (bytes[dataLength + 1] << 8);

    return calculatedCRC == receivedCRC;
}

void PLCController::sendResponse(const QByteArray &originalCommand) {
    if (!m_comm || !m_comm->checkPort()) {
        return;
    }

    // 对于写单个寄存器命令，响应就是原命令的回显
    QByteArray response = originalCommand;

    if (m_comm->write(response)) {
        qDebug() << LOG_PREFIX << "Response sent:" << response.toHex(' ');
    } else {
        qWarning() << LOG_PREFIX << "Failed to send response";
    }
}

void PLCController::resetReceiveBuffer() {
    m_receiveBuffer.clear();
}
