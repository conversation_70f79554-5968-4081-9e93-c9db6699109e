# 电机转速监控 - PLC控制功能开发文档

## 功能概述

为电机转速监控系统增加PLC控制启停功能，通过串口接收PLC发送的modbus RTU指令来控制电机监控的启动和停止，实现与手动点击startButton相同的效果。

## 架构设计

### 系统架构图

```
┌─────────────────┐    Modbus RTU    ┌──────────────────┐    Signal/Slot    ┌─────────────────────┐
│      PLC        │ ──────────────→  │  PLCController   │ ─────────────────→ │ MotorMonitorOperation│
│                 │    Serial Port   │                  │                   │                     │
└─────────────────┘                  └──────────────────┘                   └─────────────────────┘
                                              │                                        │
                                              ▼                                        ▼
                                     ┌──────────────────┐                   ┌─────────────────────┐
                                     │   IComm (UART)   │                   │   Task Control      │
                                     │                  │                   │   (Start/Stop)      │
                                     └──────────────────┘                   └─────────────────────┘
```

### 类图

```
┌─────────────────────────────────────┐
│           PLCController             │
├─────────────────────────────────────┤
│ - m_comm: IComm*                   │
│ - m_receiveTimer: QTimer*          │
│ - m_receiveBuffer: QByteArray      │
│ - m_isListening: bool              │
│ - m_deviceAddress: uint8_t         │
├─────────────────────────────────────┤
│ + setCommInterface(IComm*)         │
│ + startListening(): bool           │
│ + stopListening(): void            │
│ + setDeviceAddress(uint8_t)        │
│ - parseModbusData(QByteArray): bool│
│ - calculateCRC16(...): uint16_t    │
│ - verifyCRC16(QByteArray): bool    │
├─────────────────────────────────────┤
│ signals:                           │
│ + startCommandReceived()           │
│ + stopCommandReceived()            │
│ + connectionStatusChanged(bool)    │
│ + communicationError(QString)      │
└─────────────────────────────────────┘
                │
                ▼
┌─────────────────────────────────────┐
│      CMotorMonitorOperation        │
├─────────────────────────────────────┤
│ + m_plc_controller_: PLCController*│
├─────────────────────────────────────┤
│ + onPLCStartCommand()              │
│ + onPLCStopCommand()               │
│ + onPLCConnectionStatusChanged()   │
│ + onPLCCommunicationError()        │
└─────────────────────────────────────┘
```

## 协议设计

### Modbus RTU协议格式

采用标准Modbus RTU协议，写单个寄存器功能码(0x06)：

| 字段 | 长度 | 说明 |
|------|------|------|
| 设备地址 | 1字节 | 默认0x01 |
| 功能码 | 1字节 | 0x06 (写单个寄存器) |
| 寄存器地址 | 2字节 | 0x0001 (控制寄存器) |
| 数据 | 2字节 | 0x0001=启动, 0x0000=停止 |
| CRC16 | 2字节 | 校验码 |

### 指令示例

- **启动指令**: `01 06 00 01 00 01 [CRC16]`
- **停止指令**: `01 06 00 01 00 00 [CRC16]`

### CRC16计算

使用标准Modbus CRC16算法：
- 初始值：0xFFFF
- 多项式：0xA001
- 小端序存储

## 实现细节

### 文件结构

```
components/motorMonitor/
├── PLCController.h          # PLC控制器头文件
├── PLCController.cpp        # PLC控制器实现
├── motorMonitorOperation.h  # 修改：添加PLC控制器集成
├── motorMonitorOperation.cpp # 修改：添加PLC功能实现
└── CMakeLists.txt          # 修改：添加新源文件
```

### 关键功能实现

#### 1. 数据接收机制
- 使用定时器(50ms间隔)定期检查串口数据
- 接收到数据后启动超时定时器(100ms)
- 超时后解析完整的数据包

#### 2. 协议解析
- 验证设备地址、功能码、寄存器地址
- CRC16校验验证数据完整性
- 解析命令值并发出相应信号

#### 3. 信号槽连接
```cpp
// PLC控制器信号连接
connect(m_plc_controller_, &PLCController::startCommandReceived, 
        this, &CMotorMonitorOperation::onPLCStartCommand);
connect(m_plc_controller_, &PLCController::stopCommandReceived, 
        this, &CMotorMonitorOperation::onPLCStopCommand);
```

#### 4. 任务触发
```cpp
void CMotorMonitorOperation::onPLCStartCommand() {
    // 触发与startButton相同的效果
    mv_task_list[eREADY_STEP].flag.exec = true;
}

void CMotorMonitorOperation::onPLCStopCommand() {
    // 触发与startButton相同的效果
    mv_task_list[eCOMPLETE_STEP].flag.exec = true;
}
```

## 使用方法

### 1. UI配置
- 在界面上选择PLC端口(plcPortBox)
- 点击"open"按钮打开串口连接
- 系统自动初始化PLC控制器

### 2. PLC端配置
- 设置PLC设备地址为0x01
- 配置串口参数：115200, 8N1
- 发送标准Modbus RTU指令

### 3. 测试指令
可以使用串口调试工具发送以下十六进制指令进行测试：
- 启动：`01 06 00 01 00 01 D9 CA`
- 停止：`01 06 00 01 00 00 19 CA`

## 错误处理

### 1. 连接错误
- 串口未打开
- 通信接口未设置
- 发出communicationError信号

### 2. 协议错误
- 设备地址不匹配
- 功能码不支持
- CRC16校验失败
- 数据包长度不正确

### 3. 日志输出
所有关键操作都有详细的日志输出，便于调试和问题定位。

## 测试验证

### 1. 单元测试
- CRC16计算正确性
- 协议解析准确性
- 错误处理完整性

### 2. 集成测试
- 与现有电机监控系统的兼容性
- PLC指令与手动按钮效果一致性
- 多次启停操作稳定性

### 3. 性能测试
- 响应时间 < 100ms
- 连续指令处理能力
- 长时间运行稳定性

## 维护说明

### 1. 配置参数
- 设备地址：可通过setDeviceAddress()修改
- 超时时间：RECEIVE_TIMEOUT_MS常量
- 检查间隔：定时器间隔可调整

### 2. 扩展功能
- 支持多设备地址
- 添加更多控制指令
- 实现双向通信确认

### 3. 故障排除
- 检查串口连接和参数
- 验证PLC指令格式
- 查看日志输出定位问题
