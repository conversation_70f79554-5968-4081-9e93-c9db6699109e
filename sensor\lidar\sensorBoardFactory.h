/**************************************
 * 维系 所有设备类对象
 * 维系 父类名称/子类名称/子类对象
 * **************************************/

#ifndef SENSOR_BOARD_FACTORY_H
#define SENSOR_BOARD_FACTORY_H

#include "sensorCoinD.h"
#include "sensorMiniYj.h"
#include "faculaFactory.h"

class ISensorBoardFactory
{
public:
    enum ESensorBoard {
        eTOF_D4     = 1,
        eTOF_T4     = 2,
        eTOF_D6     = 3,
    };

    typedef struct {
        IPhotonSensor::EFaculaForm                          facula_form;
        IFaculaFactory::ERxFaculaType                       facula_type;
        IPhotonSensor::StSensorInfo                         sensor_info;
        bool                                                sensor_direction; //贴片方向
        bool                                                expand_facula_map_adjust; //
        IFaculaFactory::EFaculaExpandType                   facula_expand_type;
        IFaculaAdjust::StSingleMp                           target_tf; //目标MP TF
        IFaculaAdjust::ESymmetryAdjustType                  symm_adjust_type; //
    } StFaculaSensorInfo; //显示信息

    static QMap<ESensorBoard, StFaculaSensorInfo> sst_facula_sensor_info;

    // 单例模式
    static ISensorBoardFactory& getInstance() {
        static ISensorBoardFactory instance;
        return instance;
    }

    ITopBoard* sensorBoardCreate(const ESensorBoard &board, IComm *port_);

private:
    explicit ISensorBoardFactory();
    ~ISensorBoardFactory();
};

#endif // ISensorBoardFactory_H
