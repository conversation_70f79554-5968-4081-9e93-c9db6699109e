# 电机转速监控 - PLC控制功能使用说明

## 功能概述

电机转速监控系统现已支持PLC远程控制功能，可通过串口接收PLC发送的Modbus RTU指令来控制电机监控的启动和停止，实现与手动点击"start"按钮完全相同的效果。

## 硬件连接

### 1. 串口连接
- **PLC串口** ↔ **PC串口**（通过串口线连接）
- **串口参数**：115200, 8N1（8数据位，无校验，1停止位）
- **通信协议**：Modbus RTU

### 2. 端口配置
- 确保PLC串口与电机串口使用不同的COM端口
- 在软件界面的"PLC-com"下拉框中选择PLC对应的串口
- 点击"open"按钮打开串口连接

## PLC指令格式

### Modbus RTU协议格式

| 字段 | 长度 | 数值 | 说明 |
|------|------|------|------|
| 设备地址 | 1字节 | 0x01 | 固定设备地址 |
| 功能码 | 1字节 | 0x06 | 写单个寄存器 |
| 寄存器地址 | 2字节 | 0x0001 | 控制寄存器地址 |
| 数据值 | 2字节 | 0x0001/0x0000 | 1=启动，0=停止 |
| CRC16 | 2字节 | 计算值 | 校验码 |

### 具体指令

#### 启动指令
**十六进制**：`01 06 00 01 00 01 D9 CA`
```
01        - 设备地址
06        - 功能码（写单个寄存器）
00 01     - 寄存器地址（0x0001）
00 01     - 数据值（启动=1）
D9 CA     - CRC16校验码
```

#### 停止指令
**十六进制**：`01 06 00 01 00 00 19 CA`
```
01        - 设备地址
06        - 功能码（写单个寄存器）
00 01     - 寄存器地址（0x0001）
00 00     - 数据值（停止=0）
19 CA     - CRC16校验码
```

## 操作步骤

### 1. 软件端配置
1. 启动电机转速监控软件
2. 在"PLC-com"下拉框中选择PLC连接的串口
3. 确保"motor-com"选择的是电机连接的串口（两个端口不能相同）
4. 点击"open"按钮打开串口连接
5. 界面显示串口状态为"close"时表示连接成功

### 2. PLC端配置
1. 配置PLC串口参数：115200, 8N1
2. 编写PLC程序发送上述Modbus RTU指令
3. 确保指令格式完全正确，包括CRC16校验码

### 3. 控制操作
- **启动监控**：PLC发送启动指令 `01 06 00 01 00 01 D9 CA`
- **停止监控**：PLC发送停止指令 `01 06 00 01 00 00 19 CA`
- 软件收到指令后会自动执行相应操作，效果与手动点击按钮相同

## 状态指示

### 软件界面状态
- **串口状态**：显示"open"或"close"
- **监控状态**：start按钮文字会在"start"和"stop"之间切换
- **结果显示**：监控完成后显示测试结果

### 日志信息
软件会输出详细的日志信息，包括：
- `[PLC] Started listening for PLC commands` - PLC监听启动
- `[PLC] START command received` - 收到启动指令
- `[PLC] STOP command received` - 收到停止指令
- `[PLC] Communication error: xxx` - 通信错误信息

## 测试验证

### 1. 串口调试工具测试
可使用串口调试助手等工具发送测试指令：
- 打开串口调试工具
- 配置串口参数：115200, 8N1
- 发送十六进制指令：
  - 启动：`01 06 00 01 00 01 D9 CA`
  - 停止：`01 06 00 01 00 00 19 CA`

### 2. 功能验证
1. 发送启动指令后，软件应开始电机监控
2. 发送停止指令后，软件应停止监控并显示结果
3. 检查日志输出确认指令接收正确

## 故障排除

### 常见问题

#### 1. 指令无响应
- **检查串口连接**：确认物理连接正常
- **检查串口参数**：确认波特率、数据位等参数正确
- **检查端口选择**：确认软件中选择了正确的PLC端口
- **检查指令格式**：确认发送的指令完全正确

#### 2. CRC校验失败
- **重新计算CRC**：使用标准Modbus CRC16算法
- **检查字节序**：确认CRC16采用小端序存储
- **验证指令完整性**：确认指令长度为8字节

#### 3. 设备地址不匹配
- **确认设备地址**：默认为0x01，可在代码中修改
- **检查PLC配置**：确认PLC发送的设备地址正确

### 错误代码说明
- `Communication interface not set` - 通信接口未设置
- `Communication port not open` - 通信端口未打开
- `Invalid modbus command received` - 接收到无效的modbus指令
- `CRC16 verification failed` - CRC16校验失败

## 技术支持

如遇到问题，请提供以下信息：
1. 软件版本信息
2. PLC型号和配置
3. 串口连接方式
4. 错误日志信息
5. 发送的具体指令内容

## 注意事项

1. **端口冲突**：PLC端口和电机端口必须使用不同的COM端口
2. **指令格式**：必须严格按照Modbus RTU格式发送指令
3. **CRC校验**：CRC16校验码必须正确，否则指令会被忽略
4. **响应时间**：系统响应时间通常在100ms以内
5. **连续指令**：建议两次指令之间间隔至少200ms

## 版本信息

- **功能版本**：v1.0
- **支持软件版本**：motorMonitor v1.9.3+
- **协议版本**：Modbus RTU标准协议
- **更新日期**：2025-01-22
