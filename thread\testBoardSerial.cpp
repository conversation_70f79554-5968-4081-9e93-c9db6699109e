/*****
 * @brief: 针对每个设备的数据解析（不针对端口）
 * @author: lj
 * @date:
 *****/

#include "testBoardSerial.h"
#include <QApplication>
#include <QThread>


CTestBoardSerial::CTestBoardSerial(QObject *parent, ITestBoard *test_board_)
    : QObject(parent), m_task_id(0), m_single_receive(false), mc_test_board_(test_board_) {
}

CTestBoardSerial::~CTestBoardSerial() {
    //  if(mc_test_board_ != nullptr) delete mc_test_board_;
}

/**
 * @brief 由
 */
void CTestBoardSerial::loop(const bool &is_exc) {
    QByteArray arr;
    for (;;) {
        if (m_task_id == 1) {
            arr = mc_test_board_->portDataRead();
            if (arr.length() > 0)
                mc_test_board_->dataParsing(arr, 100);
        } else if (m_task_id == 0) {
            break;
            //          return;
        }
        //      if(!is_exc) //退出线程
        //        return;
    }
}

void CTestBoardSerial::device_change_interface(ITestBoard *test_board_) {
    // m_task_id = id;
    mc_test_board_ = test_board_;
    mc_test_board_->m_strPre.clear();
}

void CTestBoardSerial::task_id_change(const uint8_t &id) {
    m_task_id = id;
    mc_test_board_->m_strPre.clear();
}
