#ifndef MOTORMONITORWIDGET_H
#define MOTORMONITORWIDGET_H

#include "motorMonitorCustPlot.h"
#include "motorMonitorOperation.h"
#include <QDockWidget>
#include <QMessageBox>
#include <QTimer>


#ifdef LIBRARY
#define MOTORMONITORWIDGET_EXPORT Q_DECL_EXPORT
#else
#define MOTORMONITORWIDGET_EXPORT Q_DECL_IMPORT
#endif

namespace Ui {
class motorMonitorWidget;
}

class motorMonitorWidget : public QDockWidget {
    Q_OBJECT

  public:
    explicit motorMonitorWidget(QWidget *parent = nullptr);
    ~motorMonitorWidget();

  private:
    Ui::motorMonitorWidget *ui;

    NMotorMonitor::StUiConfig *mst_config_ = nullptr;  //
    // QThread* motorMonitorsub = nullptr;

    CMotorMonitorOperation *m_operation_     = nullptr;
    motorMonitorCustPlot *  motorMonitorPlot = nullptr;

    void    resultClean(void);
    void    statusBarUpdate(CMotorMonitorOperation::EProcessStep step, EExecStatus status);  //状态条更新
    void    updateConfig(NMotorMonitor::StUiConfig *config_);
    uint8_t getPwmRadio(void);
    void    plotShow(void);
    // void resultShow(const StMotorData &motor_data, const StResult &result);

    virtual void closeEvent(QCloseEvent *event) override;

  signals:
    void motorMonitorCloseSiganl(bool);

  private slots:
    void portListShow(QStringList *serial_port_list, bool port_flag);
    void devicePortListShow(QStringList *port_list_, bool port_flag);
    void readyShow(bool is_open);
    void startAckShow(EExecStatus status);
    void dataAckShow(EExecStatus status);
    void compAckShow(uint16_t status);
    //  void resultDataShow();
    void resultShow(uint16_t mean);

  private slots:
    void on_serialPushButton_clicked();
    void on_startButton_clicked();

    void on_standard_currentIndexChanged(int index);

    void on_portBox_currentIndexChanged(int index);
    void on_devPortBox_currentIndexChanged(int index);
};

#endif  // MOTORMONITORWIDGET_H
