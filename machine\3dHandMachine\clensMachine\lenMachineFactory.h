/**************************************
 * 维系 所有设备类对象,创建新设备
 * 维系 父类名称/子类名称/子类对象
 * **************************************/
#ifndef _LEN_MACHINE_FACTORY_H
#define _LEN_MACHINE_FACTORY_H

#include "qLog.h"

#include "clensMachineST.h"
#include "clensMachineQH.h"

typedef void *(*functType_)();

class CLenMachineFactory {
  public:
    ~CLenMachineFactory();

    using DeviceCreator = std::function<IClensMachine *(void)>; //IPort *, const QString &)>;

    enum class ELenMachine {
        eSHUN_TUO     = 1,
        eQING_HE     = 2,
    };

//    typedef struct {
//        QString  dev_name;
//        IDevice *device_;
//    } StDeviceInfo;

//    typedef struct {
//        QString      device;
//        QString      dev_name;
//        ICommDevice *device_;
//    } StCommDeviceInfo;


    // 单例模式
    static CLenMachineFactory &getInstance() {
        static CLenMachineFactory instance;
        return instance;
    }

    /**
     * @brief registerDeviceType 注册新设备，统一管理
     * @param device_type
     * @param model_name
     * @param creator
     */
    void registerDeviceType(const QString &device_type, const QString &model_name, DeviceCreator creator) {
        QString merge_name = mergeDeviceTypeAndName(device_type, model_name);
        m_device_model.append(merge_name);

        m_device_type[merge_name] = creator;
    }

//    static IDevice *deviceCreate(IDevice::EDeviceType device_type, const QString &proj_name) {
//        IDevice *device_ = nullptr;

//        return device_;
//    }

    /**
     * @brief commDeviceCreate, 从注册信息实例化对象
     * @param port_box_name-端口信息->绑定设备
     *  + 端口对象
     *  + 端口类型
     *  + 端口配置
     *      + 端口号
     *      + 端口baud？
     * @param proj_index-生成的设备号->绑定端口信息
     * @return
     */
    IClensMachine *lenMachineCreate(const QString &port_box_name, const QString &model_name);

    /**
     * @brief lenMachineCreate
     * @param machine
     * @param port_
     * @return
     */
    IClensMachine *lenMachineCreate(const ELenMachine &machine, IComm *port_, const uint8_t &adress);

    QStringList getDeviceModelList(void) const;

    /** */
//    CPortFactory::StPortConfig getDevicePortConfig(const IPort::EPortType &port_type, const QString &device);

    /**
     * @brief projUpdate
     * @param proj_index
     */
    //    static void instantiateDevice(ICommDevice* device_, const ICommDevice::ECommDev &proj_index) {
    //    IComm *commDeviceCreate(const QString &proj_name) {
    //        IComm *device_ = nullptr;


    //        return device_;
    //    }

    //    void        registerCommDevices(const QVector<IDevice::StDeviceProperty> &devices_property);
    //    QStringList getCommDevices(void);
    // void connectInit(const ICommDevice::ECommDev &proj_index);

  private:
    CLenMachineFactory();

    QMap<QString, QMap<QString, functType_ *>> mm_funct;

    QStringList                  m_device_model;
    QMap<QString, DeviceCreator> m_device_type;  // 设备类型枚举 -> 创建函数

    inline static QString mergeDeviceTypeAndName(const QString &device_type, const QString &name) {
        return QString("%1-%2").arg(device_type).arg(name);
    }

    inline static QPair<QString, QString> splitDeviceTypeAndName(const QString &mergedString) {
        QStringList parts = mergedString.split('-');
        if (parts.size() == 2) {
            return qMakePair(parts[0], parts[1]);
        } else {
            return qMakePair(QString(), QString());  // 返回空字符串对以表示解析失败
        }
    }
};

#endif  // CLenMachineFactory_H
